import { relations } from 'drizzle-orm'
import {
  boolean,
  json,
  numeric,
  pgEnum,
  pgTable,
  primaryKey,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core'

// Define the timer status enum
export const timerStatusEnum = pgEnum('timer_status', ['running', 'stopped'])

// Define the user table
export const users = pgTable('user', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
})

// Define user relationships
export const usersRelations = relations(users, ({ many }) => ({
  createdTimers: many(timers, { relationName: 'timerCreator' }),
  updatedTimers: many(timers, { relationName: 'timerUpdater' }),
  ownedTimers: many(timers, { relationName: 'timerOwner' }),
  createdTimerecords: many(timerecords, { relationName: 'timerecordCreator' }),
  updatedTimerecords: many(timerecords, { relationName: 'timerecordUpdater' }),
  createdCustomers: many(customers, { relationName: 'customerCreator' }),
  updatedCustomers: many(customers, { relationName: 'customerUpdater' }),
  createdRemoteServices: many(remoteServices, {
    relationName: 'remoteServiceCreator',
  }),
  updatedRemoteServices: many(remoteServices, {
    relationName: 'remoteServiceUpdater',
  }),
  createdProjectCatalogs: many(projectCatalogs, {
    relationName: 'projectCatalogCreator',
  }),
  updatedProjectCatalogs: many(projectCatalogs, {
    relationName: 'projectCatalogUpdater',
  }),
  createdProjects: many(projects, { relationName: 'projectCreator' }),
  updatedProjects: many(projects, { relationName: 'projectUpdater' }),
  createdTaskCatalogs: many(taskCatalogs, {
    relationName: 'taskCatalogCreator',
  }),
  updatedTaskCatalogs: many(taskCatalogs, {
    relationName: 'taskCatalogUpdater',
  }),
  createdTasks: many(tasks, { relationName: 'taskCreator' }),
  updatedTasks: many(tasks, { relationName: 'taskUpdater' }),
  createdTaskToTaskCatalogs: many(taskToTaskCatalogs, {
    relationName: 'taskToTaskCatalogsCreator',
  }),
  updatedTaskToTaskCatalogs: many(taskToTaskCatalogs, {
    relationName: 'taskToTaskCatalogsUpdater',
  }),
  createdTimerecordToTaskCatalogs: many(timerecordToTaskCatalogs, {
    relationName: 'timerecordToTaskCatalogsCreator',
  }),
  updatedTimerecordToTaskCatalogs: many(timerecordToTaskCatalogs, {
    relationName: 'timerecordToTaskCatalogsUpdater',
  }),
  createdQueries: many(queries, { relationName: 'queryCreator' }),
  updatedQueries: many(queries, { relationName: 'queryUpdater' }),
  createdFailedSyncs: many(failedSyncs, { relationName: 'failedSyncCreator' }),
  updatedFailedSyncs: many(failedSyncs, { relationName: 'failedSyncUpdater' }),
  failedSyncUser: many(failedSyncs, { relationName: 'failedSyncUser' }),
}))

// Define the timerecord table
export const timerecords = pgTable('timerecord', {
  id: uuid('id').primaryKey(),
  taskId: uuid('task_id')
    .notNull()
    .references(() => tasks.id),
  startTimestamp: timestamp('start_timestamp').notNull(),
  endTimestamp: timestamp('end_timestamp'),
  comment: text('comment'),
  uploadData: json('upload_data'),
  uploadSuccess: boolean('upload_success').default(false),
  uploaded: timestamp('uploaded'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define the timerecord to task catalogs junction table
export const timerecordToTaskCatalogs = pgTable(
  'timerecord_to_task_catalogs',
  {
    timerecordId: uuid('timerecord_id')
      .notNull()
      .references(() => timerecords.id, { onDelete: 'cascade' }),
    taskCatalogId: uuid('task_catalog_id')
      .notNull()
      .references(() => taskCatalogs.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by')
      .notNull()
      .references(() => users.id),
  },
  (t) => [primaryKey({ columns: [t.timerecordId, t.taskCatalogId] })]
)

// Define timerecord relationships
export const timerecordsRelations = relations(timerecords, ({ one, many }) => ({
  task: one(tasks, {
    fields: [timerecords.taskId],
    references: [tasks.id],
  }),
  timerecordToTaskCatalogs: many(timerecordToTaskCatalogs),
  creator: one(users, {
    fields: [timerecords.createdBy],
    references: [users.id],
    relationName: 'timerecordCreator',
  }),
  updater: one(users, {
    fields: [timerecords.updatedBy],
    references: [users.id],
    relationName: 'timerecordUpdater',
  }),
}))

// Define timerecord to task catalogs relationships
export const timerecordToTaskCatalogsRelations = relations(
  timerecordToTaskCatalogs,
  ({ one }) => ({
    timerecord: one(timerecords, {
      fields: [timerecordToTaskCatalogs.timerecordId],
      references: [timerecords.id],
    }),
    taskCatalog: one(taskCatalogs, {
      fields: [timerecordToTaskCatalogs.taskCatalogId],
      references: [taskCatalogs.id],
    }),
    creator: one(users, {
      fields: [timerecordToTaskCatalogs.createdBy],
      references: [users.id],
      relationName: 'timerecordToTaskCatalogsCreator',
    }),
    updater: one(users, {
      fields: [timerecordToTaskCatalogs.updatedBy],
      references: [users.id],
      relationName: 'timerecordToTaskCatalogsUpdater',
    }),
  })
)

// Define the timer table
export const timers = pgTable('timer', {
  id: uuid('id').primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  startTimestamp: timestamp('start_timestamp').notNull(),
  endTimestamp: timestamp('end_timestamp'),
  status: timerStatusEnum('status').notNull(),
  worklogId: uuid('timerecord_id')
    .notNull()
    .references(() => timerecords.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

export const timersRelations = relations(timers, ({ one }) => ({
  timerOwner: one(users, {
    relationName: 'timerOwner',
    fields: [timers.userId],
    references: [users.id],
  }),
  timerCreator: one(users, {
    fields: [timers.createdBy],
    references: [users.id],
    relationName: 'timerCreator',
  }),
  timerUpdater: one(users, {
    fields: [timers.updatedBy],
    references: [users.id],
    relationName: 'timerUpdater',
  }),
  timerecord: one(timerecords, {
    fields: [timers.worklogId],
    references: [timerecords.id],
  }),
}))

// Define the customer table
export const customers = pgTable('customer', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  rateValue: numeric('rate_value', { mode: 'number' }),
  rateCurrency: varchar('rate_currency'),
  timeNormalizationType: varchar('time_normalization_type'),
  timeNormalizationConfig: varchar('time_normalization_config'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define the remote service table
export const remoteServices = pgTable('remote_service', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  serviceType: varchar('service_type').notNull(),
  remoteUrl: varchar('remote_url'),
  remoteUser: varchar('remote_user'),
  remotePassword: text('remote_password'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define the project catalog table
export const projectCatalogs = pgTable('project_catalog', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  key: varchar('key'),
  remoteId: varchar('remote_id'),
  remoteUrl: varchar('remote_url'),
  remoteServiceId: uuid('remote_service_id').references(
    () => remoteServices.id,
    { onDelete: 'cascade' }
  ),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define the project table
export const projects = pgTable('project', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  customerId: uuid('customer_id')
    .notNull()
    .references(() => customers.id, { onDelete: 'cascade' }),
  projectCatalogId: uuid('project_catalog_id').references(
    () => projectCatalogs.id,
    { onDelete: 'set null' }
  ),
  color: varchar('color'),
  timeNormalizationType: varchar('time_normalization_type'),
  timeNormalizationConfig: varchar('time_normalization_config'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define project relationships
export const projectsRelations = relations(projects, ({ one, many }) => ({
  customer: one(customers, {
    fields: [projects.customerId],
    references: [customers.id],
  }),
  projectCatalog: one(projectCatalogs, {
    fields: [projects.projectCatalogId],
    references: [projectCatalogs.id],
  }),
  tasks: many(tasks),
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id],
    relationName: 'projectCreator',
  }),
  updater: one(users, {
    fields: [projects.updatedBy],
    references: [users.id],
    relationName: 'projectUpdater',
  }),
}))

// Define customer relationships
export const customersRelations = relations(customers, ({ many, one }) => ({
  projects: many(projects),
  creator: one(users, {
    fields: [customers.createdBy],
    references: [users.id],
    relationName: 'customerCreator',
  }),
  updater: one(users, {
    fields: [customers.updatedBy],
    references: [users.id],
    relationName: 'customerUpdater',
  }),
}))

// Define remote service relationships
export const remoteServicesRelations = relations(
  remoteServices,
  ({ many, one }) => ({
    projectCatalogs: many(projectCatalogs),
    creator: one(users, {
      fields: [remoteServices.createdBy],
      references: [users.id],
      relationName: 'remoteServiceCreator',
    }),
    updater: one(users, {
      fields: [remoteServices.updatedBy],
      references: [users.id],
      relationName: 'remoteServiceUpdater',
    }),
  })
)

// Define project catalog relationships
export const projectCatalogsRelations = relations(
  projectCatalogs,
  ({ one, many }) => ({
    remoteService: one(remoteServices, {
      fields: [projectCatalogs.remoteServiceId],
      references: [remoteServices.id],
    }),
    projects: many(projects),
    creator: one(users, {
      fields: [projectCatalogs.createdBy],
      references: [users.id],
      relationName: 'projectCatalogCreator',
    }),
    updater: one(users, {
      fields: [projectCatalogs.updatedBy],
      references: [users.id],
      relationName: 'projectCatalogUpdater',
    }),
  })
)

// Define the task status enum
export const taskStatusEnum = pgEnum('task_status', [
  'OPEN',
  'CLOSED',
  'CHARGED',
])

// Define the task catalog table
export const taskCatalogs = pgTable('task_catalog', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  key: varchar('key'),
  status: varchar('status'),
  remoteId: varchar('remote_id'),
  remoteUrl: varchar('remote_url').notNull(),
  projectCatalogId: uuid('project_catalog_id').references(
    () => projectCatalogs.id,
    { onDelete: 'cascade' }
  ),
  lastUsed: timestamp('last_used'),
  pinned: boolean('pinned').default(false),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define task catalog relationships
export const taskCatalogsRelations = relations(
  taskCatalogs,
  ({ one, many }) => ({
    projectCatalog: one(projectCatalogs, {
      fields: [taskCatalogs.projectCatalogId],
      references: [projectCatalogs.id],
    }),
    taskToTaskCatalogs: many(taskToTaskCatalogs),
    timerecordToTaskCatalogs: many(timerecordToTaskCatalogs),
    creator: one(users, {
      fields: [taskCatalogs.createdBy],
      references: [users.id],
      relationName: 'taskCatalogCreator',
    }),
    updater: one(users, {
      fields: [taskCatalogs.updatedBy],
      references: [users.id],
      relationName: 'taskCatalogUpdater',
    }),
  })
)

// Define the task table
export const tasks = pgTable('task', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  projectId: uuid('project_id')
    .notNull()
    .references(() => projects.id, { onDelete: 'cascade' }),
  status: taskStatusEnum('status').notNull(),
  defaultTask: boolean('default_task').default(false),
  pinned: boolean('pinned').default(false),
  lastUsed: timestamp('last_used'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define the task to task catalogs junction table
export const taskToTaskCatalogs = pgTable(
  'task_to_task_catalogs',
  {
    taskId: uuid('task_id')
      .notNull()
      .references(() => tasks.id, { onDelete: 'cascade' }),
    taskCatalogId: uuid('task_catalog_id')
      .notNull()
      .references(() => taskCatalogs.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    updatedBy: uuid('updated_by')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
  },
  (t) => [primaryKey({ columns: [t.taskId, t.taskCatalogId] })]
)

// Define task relationships
export const tasksRelations = relations(tasks, ({ one, many }) => ({
  project: one(projects, {
    fields: [tasks.projectId],
    references: [projects.id],
  }),
  taskToTaskCatalogs: many(taskToTaskCatalogs),
  timerecords: many(timerecords),
  creator: one(users, {
    fields: [tasks.createdBy],
    references: [users.id],
    relationName: 'taskCreator',
  }),
  updater: one(users, {
    fields: [tasks.updatedBy],
    references: [users.id],
    relationName: 'taskUpdater',
  }),
}))

// Define task to task catalogs relationships
export const taskToTaskCatalogsRelations = relations(
  taskToTaskCatalogs,
  ({ one }) => ({
    task: one(tasks, {
      fields: [taskToTaskCatalogs.taskId],
      references: [tasks.id],
    }),
    taskCatalog: one(taskCatalogs, {
      fields: [taskToTaskCatalogs.taskCatalogId],
      references: [taskCatalogs.id],
    }),
    creator: one(users, {
      fields: [taskToTaskCatalogs.createdBy],
      references: [users.id],
      relationName: 'taskToTaskCatalogsCreator',
    }),
    updater: one(users, {
      fields: [taskToTaskCatalogs.updatedBy],
      references: [users.id],
      relationName: 'taskToTaskCatalogsUpdater',
    }),
  })
)

// Define the query table
export const queries = pgTable('query', {
  id: uuid('id').primaryKey(),
  name: varchar('name').notNull(),
  position: numeric('position'),
  predicate: text('predicate').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

// Define query relationships
export const queriesRelations = relations(queries, ({ one }) => ({
  creator: one(users, {
    fields: [queries.createdBy],
    references: [users.id],
    relationName: 'queryCreator',
  }),
  updater: one(users, {
    fields: [queries.updatedBy],
    references: [users.id],
    relationName: 'queryUpdater',
  }),
}))

export const appState = pgTable('app_state', {
  id: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' })
    .primaryKey(),
  isEditDialogOpen: boolean('is_edit_dialog_open').default(false).notNull(),
  editDialogTimeRecordId: uuid('edit_dialog_time_record_id').references(
    () => timerecords.id,
    { onDelete: 'set null' }
  ),
  timestampOfIdleTimeStart: timestamp('timestamp_of_idle_time_start'),
  showingTimeoutMessage: boolean('showing_timeout_message')
    .default(false)
    .notNull(),
  runningTimerId: uuid('running_timer_id').references(() => timers.id, {
    onDelete: 'set null',
  }),
  activeClientId: uuid('active_client_id'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
})

// Define app state relationships
export const appStateRelations = relations(appState, ({ one }) => ({
  user: one(users, {
    fields: [appState.id],
    references: [users.id],
  }),
  runningTimer: one(timers, {
    fields: [appState.runningTimerId],
    references: [timers.id],
  }),
  editDialogTimeRecord: one(timerecords, {
    fields: [appState.editDialogTimeRecordId],
    references: [timerecords.id],
  }),
}))

export const failedSyncs = pgTable('failed_syncs', {
  id: uuid('id').primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  timerecordId: uuid('timerecord_id')
    .notNull()
    .references(() => timerecords.id, { onDelete: 'cascade' }),
  uploadData: json('upload_data'),
  error: text('error').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
})

export const failedSyncsRelations = relations(failedSyncs, ({ one }) => ({
  user: one(users, {
    fields: [failedSyncs.userId],
    references: [users.id],
    relationName: 'failedSyncUser',
  }),
  timerecord: one(timerecords, {
    fields: [failedSyncs.timerecordId],
    references: [timerecords.id],
  }),
  creator: one(users, {
    fields: [failedSyncs.createdBy],
    references: [users.id],
    relationName: 'failedSyncCreator',
  }),
  updater: one(users, {
    fields: [failedSyncs.updatedBy],
    references: [users.id],
    relationName: 'failedSyncUpdater',
  }),
}))

// Export all types
export type User = typeof users.$inferSelect
export type NewUser = typeof users.$inferInsert

export type Timer = typeof timers.$inferSelect
export type NewTimer = typeof timers.$inferInsert

export type Customer = typeof customers.$inferSelect
export type NewCustomer = typeof customers.$inferInsert

export type Project = typeof projects.$inferSelect
export type NewProject = typeof projects.$inferInsert

export type RemoteService = typeof remoteServices.$inferSelect
export type NewRemoteService = typeof remoteServices.$inferInsert

export type ProjectCatalog = typeof projectCatalogs.$inferSelect
export type NewProjectCatalog = typeof projectCatalogs.$inferInsert

export type TaskCatalog = typeof taskCatalogs.$inferSelect
export type NewTaskCatalog = typeof taskCatalogs.$inferInsert

export type Task = typeof tasks.$inferSelect
export type NewTask = typeof tasks.$inferInsert

export type TaskToTaskCatalog = typeof taskToTaskCatalogs.$inferSelect
export type NewTaskToTaskCatalog = typeof taskToTaskCatalogs.$inferInsert

export type Timerecord = typeof timerecords.$inferSelect
export type NewTimerecord = typeof timerecords.$inferInsert

export type TimerecordToTaskCatalog =
  typeof timerecordToTaskCatalogs.$inferSelect
export type NewTimerecordToTaskCatalog =
  typeof timerecordToTaskCatalogs.$inferInsert

export type Query = typeof queries.$inferSelect
export type NewQuery = typeof queries.$inferInsert

export type AppState = typeof appState.$inferSelect
export type NewAppState = typeof appState.$inferInsert
