/* eslint-disable */
/* tslint:disable */
// noinspection JSUnusedGlobalSymbols
// biome-ignore-all
/*
 * ------------------------------------------------------------
 * ## This file was automatically generated by drizzle-zero. ##
 * ## Any changes you make to this file will be overwritten. ##
 * ##                                                        ##
 * ## Additionally, you should also exclude this file from   ##
 * ## your linter and/or formatter to prevent it from being  ##
 * ## checked or modified.                                   ##
 * ##                                                        ##
 * ## SOURCE: https://github.com/BriefHQ/drizzle-zero        ##
 * ------------------------------------------------------------
 */

import type { ZeroCustomType } from "drizzle-zero";
import type { default as zeroSchema } from "./drizzle-zero.config";

/**
 * The Zero schema object.
 * This type is auto-generated from your Drizzle schema definition.
 */
export const schema = {
  tables: {
    appState: {
      name: "appState",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "id"
          >,
          serverName: "user_id",
        },
        isEditDialogOpen: {
          type: "boolean",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "isEditDialogOpen"
          >,
          serverName: "is_edit_dialog_open",
        },
        editDialogTimeRecordId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "editDialogTimeRecordId"
          >,
          serverName: "edit_dialog_time_record_id",
        },
        timestampOfIdleTimeStart: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "timestampOfIdleTimeStart"
          >,
          serverName: "timestamp_of_idle_time_start",
        },
        showingTimeoutMessage: {
          type: "boolean",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "showingTimeoutMessage"
          >,
          serverName: "showing_timeout_message",
        },
        runningTimerId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "runningTimerId"
          >,
          serverName: "running_timer_id",
        },
        activeClientId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "activeClientId"
          >,
          serverName: "active_client_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "appState",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
      },
      primaryKey: ["id"],
      serverName: "app_state",
    },
    customers: {
      name: "customers",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "name"
          >,
        },
        rateValue: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "rateValue"
          >,
          serverName: "rate_value",
        },
        rateCurrency: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "rateCurrency"
          >,
          serverName: "rate_currency",
        },
        timeNormalizationType: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "timeNormalizationType"
          >,
          serverName: "time_normalization_type",
        },
        timeNormalizationConfig: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "timeNormalizationConfig"
          >,
          serverName: "time_normalization_config",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "customers",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "customer",
    },
    failedSyncs: {
      name: "failedSyncs",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "id"
          >,
        },
        userId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "userId"
          >,
          serverName: "user_id",
        },
        timerecordId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "timerecordId"
          >,
          serverName: "timerecord_id",
        },
        uploadData: {
          type: "json",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "uploadData"
          >,
          serverName: "upload_data",
        },
        error: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "error"
          >,
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "failedSyncs",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "failed_syncs",
    },
    projectCatalogs: {
      name: "projectCatalogs",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "name"
          >,
        },
        key: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "key"
          >,
        },
        remoteId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "remoteId"
          >,
          serverName: "remote_id",
        },
        remoteUrl: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "remoteUrl"
          >,
          serverName: "remote_url",
        },
        remoteServiceId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "remoteServiceId"
          >,
          serverName: "remote_service_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projectCatalogs",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "project_catalog",
    },
    projects: {
      name: "projects",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "name"
          >,
        },
        customerId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "customerId"
          >,
          serverName: "customer_id",
        },
        projectCatalogId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "projectCatalogId"
          >,
          serverName: "project_catalog_id",
        },
        color: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "color"
          >,
        },
        timeNormalizationType: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "timeNormalizationType"
          >,
          serverName: "time_normalization_type",
        },
        timeNormalizationConfig: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "timeNormalizationConfig"
          >,
          serverName: "time_normalization_config",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "projects",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "project",
    },
    queries: {
      name: "queries",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "name"
          >,
        },
        position: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "position"
          >,
        },
        predicate: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "predicate"
          >,
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "queries",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "query",
    },
    remoteServices: {
      name: "remoteServices",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "name"
          >,
        },
        serviceType: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "serviceType"
          >,
          serverName: "service_type",
        },
        remoteUrl: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "remoteUrl"
          >,
          serverName: "remote_url",
        },
        remoteUser: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "remoteUser"
          >,
          serverName: "remote_user",
        },
        remotePassword: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "remotePassword"
          >,
          serverName: "remote_password",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "remoteServices",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "remote_service",
    },
    taskCatalogs: {
      name: "taskCatalogs",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "name"
          >,
        },
        key: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "key"
          >,
        },
        status: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "status"
          >,
        },
        remoteId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "remoteId"
          >,
          serverName: "remote_id",
        },
        remoteUrl: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "remoteUrl"
          >,
          serverName: "remote_url",
        },
        projectCatalogId: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "projectCatalogId"
          >,
          serverName: "project_catalog_id",
        },
        lastUsed: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "lastUsed"
          >,
          serverName: "last_used",
        },
        pinned: {
          type: "boolean",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "pinned"
          >,
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskCatalogs",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "task_catalog",
    },
    taskToTaskCatalogs: {
      name: "taskToTaskCatalogs",
      columns: {
        taskId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskToTaskCatalogs",
            "taskId"
          >,
          serverName: "task_id",
        },
        taskCatalogId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskToTaskCatalogs",
            "taskCatalogId"
          >,
          serverName: "task_catalog_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskToTaskCatalogs",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskToTaskCatalogs",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskToTaskCatalogs",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "taskToTaskCatalogs",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["taskId", "taskCatalogId"],
      serverName: "task_to_task_catalogs",
    },
    tasks: {
      name: "tasks",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "name"
          >,
        },
        projectId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "projectId"
          >,
          serverName: "project_id",
        },
        status: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "status"
          >,
        },
        defaultTask: {
          type: "boolean",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "defaultTask"
          >,
          serverName: "default_task",
        },
        pinned: {
          type: "boolean",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "pinned"
          >,
        },
        lastUsed: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "lastUsed"
          >,
          serverName: "last_used",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "tasks",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "task",
    },
    timerecordToTaskCatalogs: {
      name: "timerecordToTaskCatalogs",
      columns: {
        timerecordId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecordToTaskCatalogs",
            "timerecordId"
          >,
          serverName: "timerecord_id",
        },
        taskCatalogId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecordToTaskCatalogs",
            "taskCatalogId"
          >,
          serverName: "task_catalog_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecordToTaskCatalogs",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecordToTaskCatalogs",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecordToTaskCatalogs",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecordToTaskCatalogs",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["timerecordId", "taskCatalogId"],
      serverName: "timerecord_to_task_catalogs",
    },
    timerecords: {
      name: "timerecords",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "id"
          >,
        },
        taskId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "taskId"
          >,
          serverName: "task_id",
        },
        startTimestamp: {
          type: "number",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "startTimestamp"
          >,
          serverName: "start_timestamp",
        },
        endTimestamp: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "endTimestamp"
          >,
          serverName: "end_timestamp",
        },
        comment: {
          type: "string",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "comment"
          >,
        },
        uploadData: {
          type: "json",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "uploadData"
          >,
          serverName: "upload_data",
        },
        uploadSuccess: {
          type: "boolean",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "uploadSuccess"
          >,
          serverName: "upload_success",
        },
        uploaded: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "uploaded"
          >,
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timerecords",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "timerecord",
    },
    timers: {
      name: "timers",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "id"
          >,
        },
        userId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "userId"
          >,
          serverName: "user_id",
        },
        startTimestamp: {
          type: "number",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "startTimestamp"
          >,
          serverName: "start_timestamp",
        },
        endTimestamp: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "endTimestamp"
          >,
          serverName: "end_timestamp",
        },
        status: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "status"
          >,
        },
        worklogId: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "worklogId"
          >,
          serverName: "timerecord_id",
        },
        createdAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "createdAt"
          >,
          serverName: "created_at",
        },
        updatedAt: {
          type: "number",
          optional: true,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "updatedAt"
          >,
          serverName: "updated_at",
        },
        createdBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "createdBy"
          >,
          serverName: "created_by",
        },
        updatedBy: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "timers",
            "updatedBy"
          >,
          serverName: "updated_by",
        },
      },
      primaryKey: ["id"],
      serverName: "timer",
    },
    users: {
      name: "users",
      columns: {
        id: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "users",
            "id"
          >,
        },
        name: {
          type: "string",
          optional: false,
          customType: null as unknown as ZeroCustomType<
            typeof zeroSchema,
            "users",
            "name"
          >,
        },
      },
      primaryKey: ["id"],
      serverName: "user",
    },
  },
  relationships: {
    appState: {
      user: [
        {
          sourceField: ["id"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      runningTimer: [
        {
          sourceField: ["runningTimerId"],
          destField: ["id"],
          destSchema: "timers",
          cardinality: "one",
        },
      ],
      editDialogTimeRecord: [
        {
          sourceField: ["editDialogTimeRecordId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
    },
    customers: {
      projects: [
        {
          sourceField: ["id"],
          destField: ["customerId"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    failedSyncs: {
      user: [
        {
          sourceField: ["userId"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerecord: [
        {
          sourceField: ["timerecordId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    projectCatalogs: {
      remoteService: [
        {
          sourceField: ["remoteServiceId"],
          destField: ["id"],
          destSchema: "remoteServices",
          cardinality: "one",
        },
      ],
      projects: [
        {
          sourceField: ["id"],
          destField: ["projectCatalogId"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    projects: {
      customer: [
        {
          sourceField: ["customerId"],
          destField: ["id"],
          destSchema: "customers",
          cardinality: "one",
        },
      ],
      projectCatalog: [
        {
          sourceField: ["projectCatalogId"],
          destField: ["id"],
          destSchema: "projectCatalogs",
          cardinality: "one",
        },
      ],
      tasks: [
        {
          sourceField: ["id"],
          destField: ["projectId"],
          destSchema: "tasks",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    queries: {
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    remoteServices: {
      projectCatalogs: [
        {
          sourceField: ["id"],
          destField: ["remoteServiceId"],
          destSchema: "projectCatalogs",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    taskCatalogs: {
      projectCatalog: [
        {
          sourceField: ["projectCatalogId"],
          destField: ["id"],
          destSchema: "projectCatalogs",
          cardinality: "one",
        },
      ],
      taskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["taskCatalogId"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      timerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["taskCatalogId"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    taskToTaskCatalogs: {
      task: [
        {
          sourceField: ["taskId"],
          destField: ["id"],
          destSchema: "tasks",
          cardinality: "one",
        },
      ],
      taskCatalog: [
        {
          sourceField: ["taskCatalogId"],
          destField: ["id"],
          destSchema: "taskCatalogs",
          cardinality: "one",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    tasks: {
      project: [
        {
          sourceField: ["projectId"],
          destField: ["id"],
          destSchema: "projects",
          cardinality: "one",
        },
      ],
      taskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["taskId"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      timerecords: [
        {
          sourceField: ["id"],
          destField: ["taskId"],
          destSchema: "timerecords",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    timerecordToTaskCatalogs: {
      timerecord: [
        {
          sourceField: ["timerecordId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
      taskCatalog: [
        {
          sourceField: ["taskCatalogId"],
          destField: ["id"],
          destSchema: "taskCatalogs",
          cardinality: "one",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    timerecords: {
      task: [
        {
          sourceField: ["taskId"],
          destField: ["id"],
          destSchema: "tasks",
          cardinality: "one",
        },
      ],
      timerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["timerecordId"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      creator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      updater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
    },
    timers: {
      timerOwner: [
        {
          sourceField: ["userId"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerCreator: [
        {
          sourceField: ["createdBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerUpdater: [
        {
          sourceField: ["updatedBy"],
          destField: ["id"],
          destSchema: "users",
          cardinality: "one",
        },
      ],
      timerecord: [
        {
          sourceField: ["worklogId"],
          destField: ["id"],
          destSchema: "timerecords",
          cardinality: "one",
        },
      ],
    },
    users: {
      createdTimers: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "timers",
          cardinality: "many",
        },
      ],
      updatedTimers: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "timers",
          cardinality: "many",
        },
      ],
      ownedTimers: [
        {
          sourceField: ["id"],
          destField: ["userId"],
          destSchema: "timers",
          cardinality: "many",
        },
      ],
      createdTimerecords: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "timerecords",
          cardinality: "many",
        },
      ],
      updatedTimerecords: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "timerecords",
          cardinality: "many",
        },
      ],
      createdCustomers: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "customers",
          cardinality: "many",
        },
      ],
      updatedCustomers: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "customers",
          cardinality: "many",
        },
      ],
      createdRemoteServices: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "remoteServices",
          cardinality: "many",
        },
      ],
      updatedRemoteServices: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "remoteServices",
          cardinality: "many",
        },
      ],
      createdProjectCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "projectCatalogs",
          cardinality: "many",
        },
      ],
      updatedProjectCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "projectCatalogs",
          cardinality: "many",
        },
      ],
      createdProjects: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      updatedProjects: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "projects",
          cardinality: "many",
        },
      ],
      createdTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "taskCatalogs",
          cardinality: "many",
        },
      ],
      updatedTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "taskCatalogs",
          cardinality: "many",
        },
      ],
      createdTasks: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "tasks",
          cardinality: "many",
        },
      ],
      updatedTasks: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "tasks",
          cardinality: "many",
        },
      ],
      createdTaskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      updatedTaskToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "taskToTaskCatalogs",
          cardinality: "many",
        },
      ],
      createdTimerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      updatedTimerecordToTaskCatalogs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "timerecordToTaskCatalogs",
          cardinality: "many",
        },
      ],
      createdQueries: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "queries",
          cardinality: "many",
        },
      ],
      updatedQueries: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "queries",
          cardinality: "many",
        },
      ],
      createdFailedSyncs: [
        {
          sourceField: ["id"],
          destField: ["createdBy"],
          destSchema: "failedSyncs",
          cardinality: "many",
        },
      ],
      updatedFailedSyncs: [
        {
          sourceField: ["id"],
          destField: ["updatedBy"],
          destSchema: "failedSyncs",
          cardinality: "many",
        },
      ],
      failedSyncUser: [
        {
          sourceField: ["id"],
          destField: ["userId"],
          destSchema: "failedSyncs",
          cardinality: "many",
        },
      ],
    },
  },
} as const;

/**
 * Represents the Zero schema type.
 * This type is auto-generated from your Drizzle schema definition.
 */
export type Schema = typeof schema;
