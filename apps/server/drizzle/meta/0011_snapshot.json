{"id": "8cea5b4f-4da1-4257-b8d4-8b248631774f", "prevId": "ba854338-c90b-47b6-ad95-345e827aa31a", "version": "7", "dialect": "postgresql", "tables": {"public.app_state": {"name": "app_state", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "is_edit_dialog_open": {"name": "is_edit_dialog_open", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "edit_dialog_time_record_id": {"name": "edit_dialog_time_record_id", "type": "uuid", "primaryKey": false, "notNull": false}, "timestamp_of_idle_time_start": {"name": "timestamp_of_idle_time_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "showing_timeout_message": {"name": "showing_timeout_message", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "running_timer_id": {"name": "running_timer_id", "type": "uuid", "primaryKey": false, "notNull": false}, "active_client_id": {"name": "active_client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"app_state_user_id_user_id_fk": {"name": "app_state_user_id_user_id_fk", "tableFrom": "app_state", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app_state_edit_dialog_time_record_id_timerecord_id_fk": {"name": "app_state_edit_dialog_time_record_id_timerecord_id_fk", "tableFrom": "app_state", "tableTo": "timerecord", "columnsFrom": ["edit_dialog_time_record_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "app_state_running_timer_id_timer_id_fk": {"name": "app_state_running_timer_id_timer_id_fk", "tableFrom": "app_state", "tableTo": "timer", "columnsFrom": ["running_timer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer": {"name": "customer", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "rate_value": {"name": "rate_value", "type": "numeric", "primaryKey": false, "notNull": false}, "rate_currency": {"name": "rate_currency", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_type": {"name": "time_normalization_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_config": {"name": "time_normalization_config", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"customer_created_by_user_id_fk": {"name": "customer_created_by_user_id_fk", "tableFrom": "customer", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_updated_by_user_id_fk": {"name": "customer_updated_by_user_id_fk", "tableFrom": "customer", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.failed_syncs": {"name": "failed_syncs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "timerecord_id": {"name": "timerecord_id", "type": "uuid", "primaryKey": false, "notNull": true}, "upload_data": {"name": "upload_data", "type": "json", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"failed_syncs_user_id_user_id_fk": {"name": "failed_syncs_user_id_user_id_fk", "tableFrom": "failed_syncs", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "failed_syncs_timerecord_id_timerecord_id_fk": {"name": "failed_syncs_timerecord_id_timerecord_id_fk", "tableFrom": "failed_syncs", "tableTo": "timerecord", "columnsFrom": ["timerecord_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "failed_syncs_created_by_user_id_fk": {"name": "failed_syncs_created_by_user_id_fk", "tableFrom": "failed_syncs", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "failed_syncs_updated_by_user_id_fk": {"name": "failed_syncs_updated_by_user_id_fk", "tableFrom": "failed_syncs", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_catalog": {"name": "project_catalog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_id": {"name": "remote_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_url": {"name": "remote_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_service_id": {"name": "remote_service_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_catalog_remote_service_id_remote_service_id_fk": {"name": "project_catalog_remote_service_id_remote_service_id_fk", "tableFrom": "project_catalog", "tableTo": "remote_service", "columnsFrom": ["remote_service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_catalog_created_by_user_id_fk": {"name": "project_catalog_created_by_user_id_fk", "tableFrom": "project_catalog", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_catalog_updated_by_user_id_fk": {"name": "project_catalog_updated_by_user_id_fk", "tableFrom": "project_catalog", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_catalog_id": {"name": "project_catalog_id", "type": "uuid", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_type": {"name": "time_normalization_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "time_normalization_config": {"name": "time_normalization_config", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_customer_id_customer_id_fk": {"name": "project_customer_id_customer_id_fk", "tableFrom": "project", "tableTo": "customer", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_project_catalog_id_project_catalog_id_fk": {"name": "project_project_catalog_id_project_catalog_id_fk", "tableFrom": "project", "tableTo": "project_catalog", "columnsFrom": ["project_catalog_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "project_created_by_user_id_fk": {"name": "project_created_by_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_updated_by_user_id_fk": {"name": "project_updated_by_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.query": {"name": "query", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "numeric", "primaryKey": false, "notNull": false}, "predicate": {"name": "predicate", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"query_created_by_user_id_fk": {"name": "query_created_by_user_id_fk", "tableFrom": "query", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "query_updated_by_user_id_fk": {"name": "query_updated_by_user_id_fk", "tableFrom": "query", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.remote_service": {"name": "remote_service", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "remote_url": {"name": "remote_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_user": {"name": "remote_user", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_password": {"name": "remote_password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"remote_service_created_by_user_id_fk": {"name": "remote_service_created_by_user_id_fk", "tableFrom": "remote_service", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "remote_service_updated_by_user_id_fk": {"name": "remote_service_updated_by_user_id_fk", "tableFrom": "remote_service", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_catalog": {"name": "task_catalog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_id": {"name": "remote_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remote_url": {"name": "remote_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_catalog_id": {"name": "project_catalog_id", "type": "uuid", "primaryKey": false, "notNull": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "pinned": {"name": "pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"task_catalog_project_catalog_id_project_catalog_id_fk": {"name": "task_catalog_project_catalog_id_project_catalog_id_fk", "tableFrom": "task_catalog", "tableTo": "project_catalog", "columnsFrom": ["project_catalog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_catalog_created_by_user_id_fk": {"name": "task_catalog_created_by_user_id_fk", "tableFrom": "task_catalog", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_catalog_updated_by_user_id_fk": {"name": "task_catalog_updated_by_user_id_fk", "tableFrom": "task_catalog", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_to_task_catalogs": {"name": "task_to_task_catalogs", "schema": "", "columns": {"task_id": {"name": "task_id", "type": "uuid", "primaryKey": false, "notNull": true}, "task_catalog_id": {"name": "task_catalog_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"task_to_task_catalogs_task_id_task_id_fk": {"name": "task_to_task_catalogs_task_id_task_id_fk", "tableFrom": "task_to_task_catalogs", "tableTo": "task", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_to_task_catalogs_task_catalog_id_task_catalog_id_fk": {"name": "task_to_task_catalogs_task_catalog_id_task_catalog_id_fk", "tableFrom": "task_to_task_catalogs", "tableTo": "task_catalog", "columnsFrom": ["task_catalog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_to_task_catalogs_created_by_user_id_fk": {"name": "task_to_task_catalogs_created_by_user_id_fk", "tableFrom": "task_to_task_catalogs", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_to_task_catalogs_updated_by_user_id_fk": {"name": "task_to_task_catalogs_updated_by_user_id_fk", "tableFrom": "task_to_task_catalogs", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"task_to_task_catalogs_task_id_task_catalog_id_pk": {"name": "task_to_task_catalogs_task_id_task_catalog_id_pk", "columns": ["task_id", "task_catalog_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task": {"name": "task", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "task_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "default_task": {"name": "default_task", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "pinned": {"name": "pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"task_project_id_project_id_fk": {"name": "task_project_id_project_id_fk", "tableFrom": "task", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_created_by_user_id_fk": {"name": "task_created_by_user_id_fk", "tableFrom": "task", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_updated_by_user_id_fk": {"name": "task_updated_by_user_id_fk", "tableFrom": "task", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timerecord_to_task_catalogs": {"name": "timerecord_to_task_catalogs", "schema": "", "columns": {"timerecord_id": {"name": "timerecord_id", "type": "uuid", "primaryKey": false, "notNull": true}, "task_catalog_id": {"name": "task_catalog_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"timerecord_to_task_catalogs_timerecord_id_timerecord_id_fk": {"name": "timerecord_to_task_catalogs_timerecord_id_timerecord_id_fk", "tableFrom": "timerecord_to_task_catalogs", "tableTo": "timerecord", "columnsFrom": ["timerecord_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "timerecord_to_task_catalogs_task_catalog_id_task_catalog_id_fk": {"name": "timerecord_to_task_catalogs_task_catalog_id_task_catalog_id_fk", "tableFrom": "timerecord_to_task_catalogs", "tableTo": "task_catalog", "columnsFrom": ["task_catalog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "timerecord_to_task_catalogs_created_by_user_id_fk": {"name": "timerecord_to_task_catalogs_created_by_user_id_fk", "tableFrom": "timerecord_to_task_catalogs", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timerecord_to_task_catalogs_updated_by_user_id_fk": {"name": "timerecord_to_task_catalogs_updated_by_user_id_fk", "tableFrom": "timerecord_to_task_catalogs", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"timerecord_to_task_catalogs_timerecord_id_task_catalog_id_pk": {"name": "timerecord_to_task_catalogs_timerecord_id_task_catalog_id_pk", "columns": ["timerecord_id", "task_catalog_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timerecord": {"name": "timerecord", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "task_id": {"name": "task_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_timestamp": {"name": "start_timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_timestamp": {"name": "end_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "upload_data": {"name": "upload_data", "type": "json", "primaryKey": false, "notNull": false}, "upload_success": {"name": "upload_success", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "uploaded": {"name": "uploaded", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"timerecord_task_id_task_id_fk": {"name": "timerecord_task_id_task_id_fk", "tableFrom": "timerecord", "tableTo": "task", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timerecord_created_by_user_id_fk": {"name": "timerecord_created_by_user_id_fk", "tableFrom": "timerecord", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "timerecord_updated_by_user_id_fk": {"name": "timerecord_updated_by_user_id_fk", "tableFrom": "timerecord", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timer": {"name": "timer", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_timestamp": {"name": "start_timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_timestamp": {"name": "end_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "timer_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "timerecord_id": {"name": "timerecord_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"timer_user_id_user_id_fk": {"name": "timer_user_id_user_id_fk", "tableFrom": "timer", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "timer_timerecord_id_timerecord_id_fk": {"name": "timer_timerecord_id_timerecord_id_fk", "tableFrom": "timer", "tableTo": "timerecord", "columnsFrom": ["timerecord_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "timer_created_by_user_id_fk": {"name": "timer_created_by_user_id_fk", "tableFrom": "timer", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "timer_updated_by_user_id_fk": {"name": "timer_updated_by_user_id_fk", "tableFrom": "timer", "tableTo": "user", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.task_status": {"name": "task_status", "schema": "public", "values": ["OPEN", "CLOSED", "CHARGED"]}, "public.timer_status": {"name": "timer_status", "schema": "public", "values": ["running", "stopped"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}