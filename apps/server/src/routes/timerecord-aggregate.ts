import { zValidator } from '@hono/zod-validator'
import { and, between, eq, sql } from 'drizzle-orm'
import { round, sumBy } from 'es-toolkit'
import { Hono } from 'hono'
import { z } from 'zod'
import { db } from '~/db'
import { jwtAuth } from '~/middleware/auth'
import {
  customers,
  projects,
  tasks,
  timerecords,
} from '../../drizzle/drizzle-schema'

export const timerecordAggregateRouter = new Hono()

// Apply JWT authentication middleware to all routes
timerecordAggregateRouter.use('*', jwtAuth)

// Schema for timerecord aggregation request
const timerecordAggregateSchema = z.object({
  customerIds: z.array(z.string().uuid()).optional(),
  projectIds: z.array(z.string().uuid()).optional(),
  taskIds: z.array(z.string().uuid()).optional(),
  startTimestamp: z.number().int().positive(),
  endTimestamp: z.number().int().positive(),
})

// POST endpoint for timerecord aggregation
timerecordAggregateRouter.post(
  '/',
  zValidator('json', timerecordAggregateSchema),
  async (c) => {
    try {
      const { customerIds, projectIds, taskIds, startTimestamp, endTimestamp } =
        c.req.valid('json')

      // Convert timestamps to Date objects
      const startDate = new Date(startTimestamp)
      const endDate = new Date(endTimestamp)

      // Get the authenticated user ID from the context
      const { userId } = c.get('auth')

      // Build the query conditions
      const conditions = []

      // Add date range condition
      conditions.push(between(timerecords.startTimestamp, startDate, endDate))

      // Add user ID condition to filter by the authenticated user
      conditions.push(eq(timerecords.createdBy, userId))

      // Add filter conditions if provided
      if (taskIds && taskIds.length > 0) {
        conditions.push(
          sql`${timerecords.taskId} IN (${sql.join(
            taskIds.map((id) => sql`${id}`),
            sql`, `
          )})`
        )
      }

      if (projectIds && projectIds.length > 0) {
        conditions.push(
          sql`${tasks.projectId} IN (${sql.join(
            projectIds.map((id) => sql`${id}`),
            sql`, `
          )})`
        )
      }

      if (customerIds && customerIds.length > 0) {
        conditions.push(
          sql`${projects.customerId} IN (${sql.join(
            customerIds.map((id) => sql`${id}`),
            sql`, `
          )})`
        )
      }

      // Calculate duration as endTimestamp - startTimestamp
      // For records without endTimestamp, use current time
      const durationExpression = sql`
        CASE
          WHEN ${timerecords.endTimestamp} IS NOT NULL
          THEN CAST(EXTRACT(EPOCH FROM (${timerecords.endTimestamp} - ${timerecords.startTimestamp})) * 1000 as DOUBLE precision)
          ELSE CAST(EXTRACT(EPOCH FROM (NOW() - ${timerecords.startTimestamp})) * 1000 as DOUBLE precision)
        END
      `

      // Execute the query with aggregation
      const result = await db
        .select({
          customerId: customers.id,
          customerName: customers.name,
          customerRate: customers.rateValue,
          customerCurrency: customers.rateCurrency,
          projectId: projects.id,
          projectName: projects.name,
          taskId: tasks.id,
          taskName: tasks.name,
          totalDuration:
            sql`CAST(SUM(${durationExpression}) AS DOUBLE precision)`.as(
              'total_duration'
            ),
        })
        .from(timerecords)
        .innerJoin(tasks, eq(timerecords.taskId, tasks.id))
        .innerJoin(projects, eq(tasks.projectId, projects.id))
        .innerJoin(customers, eq(projects.customerId, customers.id))
        .where(and(...conditions))
        .groupBy(
          customers.id,
          customers.name,
          customers.rateValue,
          customers.rateCurrency,
          projects.id,
          projects.name,
          tasks.id,
          tasks.name
        )
        .orderBy(customers.name, projects.name, tasks.name)
        .execute()

      const totalDuration = sumBy(result, (it) =>
        round(it.totalDuration as number, 4)
      )
      const resultsWithPrice = result.map((it) => {
        const rate = it.customerRate ?? 0
        const durationInHours = (it.totalDuration as number) / 1000 / 60 / 60
        const price = durationInHours * rate
        return {
          ...it,
          price,
          durationInHours,
        }
      })

      return c.json({
        success: true,
        data: resultsWithPrice,
        totalDuration,
      })
    } catch (error) {
      console.error('Timerecord aggregation failed:', error)
      return c.json(
        {
          success: false,
          message: 'Timerecord aggregation failed',
          error: error instanceof Error ? error.message : String(error),
        },
        500
      )
    }
  }
)
