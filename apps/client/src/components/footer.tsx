import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { type FC, useCallback } from 'react'
import { timeRecordEditorClosed } from '~/lib/model/app-state'
import type { TimeRecordInput } from '~/modules/timerecords/time-record-model'
import { type TimeRecord, TimeRecordEditor } from './time-record-editor'

export const Footer: FC = () => {
  const z = useZero<Schema>()
  const [appState, appStateDetails] = useQuery(
    z.query.appState.where('id', '=', z.userID).related('runningTimer').one(),
    {
      ttl: 'forever',
    }
  )

  const timeRecordId = appState?.editDialogTimeRecordId

  const isEditDialogOpen =
    appStateDetails.type === 'complete' && appState?.isEditDialogOpen

  const [timeRecord, timeRecordsDetails] = useQuery(
    z.query.timerecords
      .where('id', '=', timeRecordId ?? '')
      .related('task', (q) =>
        q.related('project', (q) => q.related('customer'))
      )
      .one(),
    {
      enabled: !!timeRecordId,
      ttl: 'forever',
    }
  )
  const timeRecordComplete =
    timeRecordsDetails.type === 'complete' && !!timeRecord

  const editorSaving = useCallback(
    (tr: TimeRecordInput) => {
      timeRecordEditorClosed(z, tr).catch((error) => {
        console.error('Error saving time record:', error)
      })
    },
    [z]
  )
  return (
    <>
      {timeRecordComplete && isEditDialogOpen && timeRecord && (
        <TimeRecordEditor
          timeRecord={timeRecord as TimeRecord}
          isOpen={isEditDialogOpen && timeRecordComplete}
          onClose={() => {}}
          onSave={editorSaving}
          onDelete={() => {
            throw new Error('Not implemented')
          }}
        />
      )}
    </>
  )
}
