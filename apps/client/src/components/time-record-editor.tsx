import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useStore } from '@tanstack/react-form'
import { invariant } from 'es-toolkit'
import {
  type EventHandler,
  type FC,
  type SyntheticEvent,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { uuidv4 } from 'uuidv7'
import { ConfirmationDialog } from '~/components/confirmation-dialog'
import { TaskCatalogPicker } from '~/components/task-catalog-picker'
import { TaskPicker } from '~/components/task-picker'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { useAppForm } from '~/components/ui/tanstack-form'
import { useRefify } from '~/lib/hooks/use-refify'
import {
  dbLinkTimeRecordToTaskCatalog,
  dbUnlinkTimeRecordFromTaskCatalog,
} from '~/lib/services/database-service'
import { isNonEmptyArray } from '~/lib/utils/array-utils'
import { isTruthy } from '~/lib/utils/property-utils'
import {
  type TimeNormalizationType,
  normalizeOptionalTime,
  normalizeTime,
} from '~/lib/utils/time-normalization'
import {
  type TimeRecordInput,
  TimeRecordSchema,
} from '~/modules/timerecords/time-record-model'
import { formatOptDateTimeFromTimestamp } from '~/utils/date'
import { Combobox, CommandOptions } from './combobox'
import { type DateTimeFormatDefaults, DatetimeInput } from './date-time-input'
import { Input } from './ui/input'

function formatTaskCatalogLabel(taskCatalog: {
  name: string
  key?: string | null
  projectCatalog?: { name: string; remoteService?: { name: string } } | null
}): string {
  const projectName = taskCatalog.projectCatalog?.name
    ? `${taskCatalog.projectCatalog.name} - `
    : ''
  const remoteServiceName =
    taskCatalog.projectCatalog?.remoteService?.name ?? ''
  const keyPart = taskCatalog.key ? ` (${taskCatalog.key})` : ''

  return `${remoteServiceName}: ${projectName}${taskCatalog.name}${keyPart}`
}

const TaskLinkedTaskCatalogPicker: FC<{
  value?: string
  onChange?: (taskCatalogId: string) => void
  taskId: string
  display?: 'combobox' | 'search'
}> = ({ value, onChange, taskId, display = 'combobox' }) => {
  const z = useZero<Schema>()

  // Query task catalogs linked to the specific task
  const [taskToTaskCatalogs = []] = useQuery(
    z.query.taskToTaskCatalogs
      .where('taskId', '=', taskId)
      .related('taskCatalog', (q) =>
        q.related('projectCatalog', (q) => q.related('remoteService'))
      ),
    {
      ttl: 'forever',
    }
  )

  const taskCatalogOptions = useMemo(
    () =>
      taskToTaskCatalogs
        .filter((relation) => relation.taskCatalog)
        .map((relation) => {
          const taskCatalog = relation.taskCatalog
          if (!taskCatalog) return null
          return {
            value: taskCatalog.id,
            label: formatTaskCatalogLabel(taskCatalog),
          }
        })
        .filter(isTruthy),
    [taskToTaskCatalogs]
  )

  return (
    <div className="relative">
      {display === 'combobox' ? (
        <Combobox
          value={value}
          onChange={onChange}
          options={taskCatalogOptions}
          shouldFilter={true}
        />
      ) : (
        <CommandOptions
          options={taskCatalogOptions}
          value={value}
          onChange={onChange}
          shouldFilter={true}
        />
      )}
    </div>
  )
}

// Component to link/unlink task catalogs to time records
const TaskCatalogLinker: FC<{
  timeRecord: TimeRecord | null
  onLinkedTaskCatalogsChange: () => void
}> = ({ timeRecord, onLinkedTaskCatalogsChange }) => {
  const z = useZero<Schema>()
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false)
  const [selectedTaskCatalogId, setSelectedTaskCatalogId] = useState<
    string | null
  >(null)

  // Don't show anything if there's no time record
  if (!timeRecord?.id) {
    return null
  }

  // Query currently linked task catalogs
  const [linkedTaskCatalogs = []] = useQuery(
    z.query.timerecordToTaskCatalogs
      .where('timerecordId', '=', timeRecord.id)
      .related('taskCatalog', (q) =>
        q.related('projectCatalog', (q) => q.related('remoteService'))
      ),
    {
      ttl: 'forever',
    }
  )

  // Get the project catalog ID for filtering
  const projectCatalogId = timeRecord.task.project.projectCatalogId

  // Determine if we can link task catalogs
  const canLinkTaskCatalogs = useMemo(() => {
    if (timeRecord.task.defaultTask) {
      // For default tasks, allow linking if project has a project catalog
      return !!timeRecord.task.project.projectCatalogId
    }
    // For non-default tasks, we'll let the TaskCatalogPicker handle the filtering
    return true
  }, [timeRecord.task])

  // Handle linking a task catalog
  const handleLinkTaskCatalog = useCallback(async () => {
    if (!selectedTaskCatalogId || !timeRecord?.id) return

    try {
      // Check if already linked
      const isAlreadyLinked = linkedTaskCatalogs.some(
        (relation) => relation.taskCatalog?.id === selectedTaskCatalogId
      )

      if (!isAlreadyLinked) {
        await dbLinkTimeRecordToTaskCatalog({
          dbContext: { z, tx: z.mutate },
          timeRecordId: timeRecord.id,
          taskCatalogId: selectedTaskCatalogId,
        })

        onLinkedTaskCatalogsChange()
      }

      setIsLinkDialogOpen(false)
      setSelectedTaskCatalogId(null)
    } catch (error) {
      console.error('Error linking task catalog:', error)
    }
  }, [
    selectedTaskCatalogId,
    timeRecord?.id,
    linkedTaskCatalogs,
    z,
    onLinkedTaskCatalogsChange,
  ])

  // Handle unlinking a task catalog
  const handleUnlinkTaskCatalog = useCallback(
    async (taskCatalogId: string) => {
      if (!timeRecord?.id) return

      try {
        await dbUnlinkTimeRecordFromTaskCatalog(z, timeRecord.id, taskCatalogId)
        onLinkedTaskCatalogsChange()
      } catch (error) {
        console.error('Error unlinking task catalog:', error)
      }
    },
    [timeRecord?.id, z, onLinkedTaskCatalogsChange]
  )

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium leading-none">Task Catalogs</div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setIsLinkDialogOpen(true)}
          disabled={!canLinkTaskCatalogs}
        >
          Link Task Catalog
        </Button>
      </div>

      {/* Display linked task catalogs with unlink buttons */}
      {isNonEmptyArray(linkedTaskCatalogs) && (
        <div className="space-y-1">
          {linkedTaskCatalogs
            .filter((relation) => relation.taskCatalog)
            .map((relation) => {
              const taskCatalog = relation.taskCatalog
              if (!taskCatalog) return null
              return (
                <div
                  key={taskCatalog.id}
                  className="flex items-center justify-between text-sm text-muted-foreground bg-muted px-2 py-1 rounded"
                >
                  <span>{formatTaskCatalogLabel(taskCatalog)}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleUnlinkTaskCatalog(taskCatalog.id)}
                    className="h-auto p-1 text-xs"
                  >
                    ×
                  </Button>
                </div>
              )
            })}
        </div>
      )}

      {/* Link Task Catalog Dialog */}
      <Dialog
        open={isLinkDialogOpen}
        onOpenChange={(open) => !open && setIsLinkDialogOpen(false)}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Link Task Catalog</DialogTitle>
            <DialogDescription>
              {timeRecord.task.defaultTask
                ? `Select a task catalog from the ${timeRecord.task.project.name} project to link.`
                : `Select a task catalog that is linked to the "${timeRecord.task.name}" task.`}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {timeRecord.task.defaultTask ? (
              <TaskCatalogPicker
                value={selectedTaskCatalogId || undefined}
                onChange={setSelectedTaskCatalogId}
                projectCatalogId={projectCatalogId || undefined}
                display="combobox"
              />
            ) : (
              <TaskLinkedTaskCatalogPicker
                value={selectedTaskCatalogId || undefined}
                onChange={setSelectedTaskCatalogId}
                taskId={timeRecord.task.id}
                display="combobox"
              />
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsLinkDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleLinkTaskCatalog}
              disabled={!selectedTaskCatalogId}
            >
              Link Task Catalog
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Define the TimeRecord interface

const dateTimeFormat: DateTimeFormatDefaults = [
  ['days', 'months', 'years'],
  ['hours', 'minutes'],
]
export interface SlimTimeRecord {
  id: string
  taskId: string
  startTimestamp: number
  endTimestamp: number | null
  comment?: string
  createdAt?: number
  updatedAt?: number
  createdBy?: string
  updatedBy?: string
}
export interface TimeRecord extends SlimTimeRecord {
  task: {
    id: string
    name: string
    defaultTask?: boolean
    project: {
      id: string
      name: string
      customerId: string
      projectCatalogId?: string | null
      timeNormalizationType?: string
      timeNormalizationConfig?: string
      customer?: {
        id: string
        name: string
        timeNormalizationType?: string
        timeNormalizationConfig?: string
      }
    }
  }
}

interface TimeRecordEditorProps {
  date?: string
  timeRecord: TimeRecord | null
  isOpen: boolean
  onClose: () => void
  onSave: (timeRecord: TimeRecordInput) => void
  onDelete?: (timeRecord: SlimTimeRecord) => void
}

export const TimeRecordEditor: FC<TimeRecordEditorProps> = (props) => {
  const [timeRecord] = useState<TimeRecord | null>(props.timeRecord)
  const newProps = useMemo(
    () => ({ ...props, timeRecord }),
    [props, timeRecord]
  )
  return <TimeRecordEditorInner {...newProps} />
}

// TimeRecordEditor component
const TimeRecordEditorInner: FC<TimeRecordEditorProps> = memo(
  ({ timeRecord, isOpen, onClose, onSave, onDelete, date }) => {
    // Create refs for the input fields
    const commentRef = useRef<HTMLInputElement>(null)
    const timeRecordRef = useRefify(timeRecord)

    useEffect(() => {
      console.log(
        'TimeRecordEditor: initial render task ',
        timeRecordRef.current?.taskId,
        'timerecord',
        timeRecordRef.current
      )
    }, [timeRecordRef])

    const normalizationConfig = useMemo(
      () => lookupTimeNormalizationConfig(timeRecord?.task),
      [timeRecord?.task]
    )

    // We'll use the TaskFilter component for task selection

    // Default values for the form
    const defaultValues: TimeRecordInput = useMemo(() => {
      console.log('updating default values', { timeRecord })

      const { startTimestamp, endTimestamp } = resolveStartEndTimestamp(
        timeRecord,
        date
      )
      return {
        id: timeRecord?.id || '',
        taskId: timeRecord?.taskId || '',
        startTimestamp: normalizeTime(
          startTimestamp,
          normalizationConfig?.type,
          normalizationConfig?.config
        ),
        endTimestamp:
          normalizeOptionalTime(
            endTimestamp ?? undefined,
            normalizationConfig?.type,
            normalizationConfig?.config
          ) ?? null,
        // endTimestamp: timeRecord?.endTimestamp || undefined,
        comment: timeRecord?.comment || '',
      }
    }, [date, timeRecord, normalizationConfig])

    // Create form with TanStack Form
    const form = useAppForm({
      defaultValues,
      onSubmit: async ({ value }) => {
        console.log('form submit2', { value })
        const updatedRecord: TimeRecordInput = {
          id: value.id || uuidv4(),
          taskId: value.taskId,
          startTimestamp: value.startTimestamp,
          endTimestamp: value.endTimestamp,
          comment: value.comment,
        }

        onSave(updatedRecord)
        onClose()
      },
      // Use form-level validation with Valibot schema
      validators: {
        onChange: TimeRecordSchema,
      },
    })

    // Re-apply normalization when task changes (which changes timeNormalizationType)
    useEffect(() => {
      // Skip initial render and only run when form is already initialized
      if (!isOpen || !form.getFieldValue('startTimestamp')) return

      // Get current values
      const currentStartTime = form.getFieldValue('startTimestamp')
      const currentEndTime = form.getFieldValue('endTimestamp')

      // Apply normalization with new settings
      const normalizedStartTime = normalizeTime(
        currentStartTime,
        normalizationConfig?.type,
        normalizationConfig?.config
      )

      const normalizedEndTime = normalizeOptionalTime(
        currentEndTime ?? undefined,
        normalizationConfig?.type,
        normalizationConfig?.config
      )

      // Only update if normalization changed the values
      if (
        normalizedStartTime !== currentStartTime ||
        normalizedEndTime !== currentEndTime
      ) {
        console.log('normalizing time', {
          currentStartTime: formatOptDateTimeFromTimestamp(currentStartTime),
          currentEndTime: formatOptDateTimeFromTimestamp(
            currentEndTime ?? undefined
          ),
          normalizedStartTime:
            formatOptDateTimeFromTimestamp(normalizedStartTime),
          normalizedEndTime: formatOptDateTimeFromTimestamp(normalizedEndTime),
        })
        form.setFieldValue('startTimestamp', normalizedStartTime)
        if (normalizedEndTime) {
          form.setFieldValue('endTimestamp', normalizedEndTime)
        }
      }
    }, [normalizationConfig, isOpen, form])

    // State for delete confirmation dialog
    const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

    // Handle delete button click
    const handleDeleteClick = useCallback(() => {
      setIsDeleteConfirmOpen(true)
    }, [])

    // Handle confirmed deletion
    const handleConfirmDelete = useCallback(() => {
      if (timeRecord && onDelete) {
        onDelete(timeRecord)
        setIsDeleteConfirmOpen(false)
        onClose()
      }
    }, [timeRecord, onDelete, onClose])

    const doFormSubmit = useCallback<
      EventHandler<SyntheticEvent<HTMLFormElement>>
    >(
      (e) => {
        console.log('form submit', { e })
        e.preventDefault()
        e.stopPropagation()
        form
          .handleSubmit()
          .catch((e) => console.error('error submitting form', e))
      },
      [form]
    )

    const startTimeRef = useRef<HTMLInputElement>(null)
    const endTimeRef = useRef<HTMLInputElement>(null)
    const endButtonRef = useRef<HTMLButtonElement>(null)

    // Handle keyboard shortcuts
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        // Handle CMD+ENTER (Mac) or CTRL+ENTER (Windows/Linux) to submit the form
        if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
          e.preventDefault()
          e.stopPropagation()
          doFormSubmit(e as unknown as SyntheticEvent<HTMLFormElement>)
          return
        }

        // Field focus shortcuts
        if (e.metaKey || e.ctrlKey) {
          switch (e.key) {
            case '1': {
              e.preventDefault()
              const timeFields =
                startTimeRef.current?.getElementsByTagName('input') ?? []
              if (timeFields?.length) {
                timeFields[0].focus()
              }
              // startTimeRef.current?.focus()
              break
            }
            case '2': {
              e.preventDefault()
              const timeFields =
                endTimeRef.current?.getElementsByTagName('input') ?? []
              if (timeFields?.length) {
                timeFields[0].focus()
              }
              endButtonRef.current?.focus()
              break
            }
            case '3':
              e.preventDefault()
              commentRef.current?.focus()
              break
            // We can't directly focus the TaskFilter component or DatetimeInput as they don't support direct focus
            // We'll handle this differently in the future if needed
          }
        }
      },
      [doFormSubmit]
    )

    const { values } = useStore(form.store, (state) => ({
      values: state.values,
    }))
    // const errors = useStore(form.store, (state) => state.errors)

    console.log('form values', {
      start: values.startTimestamp,
      end: values.endTimestamp,
      startStr: formatOptDateTimeFromTimestamp(values.startTimestamp),
      endStr: formatOptDateTimeFromTimestamp(values.endTimestamp ?? undefined),
      startInput: values.startTimestamp
        ? formatDateTimeForInput(values.startTimestamp)
        : '',
      endInput: values.endTimestamp
        ? formatDateTimeForInput(values.endTimestamp)
        : '',
      normalizationConfig,
    })

    const onStartTimeChanged = useCallback(
      (e: Date | undefined) => {
        console.log('start time change', e)
        invariant(e, 'dont have new start time')
        const normalizedStartTime = normalizeTime(
          e.getTime(),
          normalizationConfig?.type,
          normalizationConfig?.config
        )
        form.setFieldValue('startTimestamp', normalizedStartTime)
      },
      [form, normalizationConfig]
    )

    const ignoreEndTimeChanged = useRef(false)

    const onClearEndTimestamp = useCallback(() => {
      console.log('clearing end time')
      ignoreEndTimeChanged.current = true
      form.setFieldValue('endTimestamp', null)
      setTimeout(() => {
        ignoreEndTimeChanged.current = false
      }, 100)
    }, [form])

    const onEndTimeChanged = useCallback(
      (e: Date | undefined) => {
        if (ignoreEndTimeChanged.current) {
          console.log('ignoring end timestamp change', e)
          return
        }

        const normalizedEndTime = normalizeOptionalTime(
          e?.getTime() ?? undefined,
          normalizationConfig?.type,
          normalizationConfig?.config
        )
        form.setFieldValue('endTimestamp', normalizedEndTime ?? null)
      },
      [form, normalizationConfig]
    )

    if (!isOpen) {
      return null
    }

    return (
      <>
        <Dialog
          open={isOpen}
          onOpenChange={(open) => !open && onClose()}
          modal={true}
        >
          <DialogContent className="sm:max-w-[500px] max-h-svh overflow-y-scroll">
            <form.AppForm>
              <form onSubmit={doFormSubmit} onKeyDown={handleKeyDown}>
                <DialogHeader>
                  <DialogTitle>
                    {timeRecord ? 'Edit' : 'Add'} Time Record
                  </DialogTitle>
                  <DialogDescription>
                    Record your time spent on tasks.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <form.AppField name="taskId">
                    {(field) => (
                      <field.FormItem>
                        <field.FormLabel>Task</field.FormLabel>
                        <field.FormControl>
                          <TaskPicker
                            value={field.state.value}
                            onChange={(value) => {
                              field.handleChange(value)
                              // When task changes, we need to re-apply time normalization
                              // This will trigger a re-fetch of task details with the new task

                              // If we have current timestamps, normalize them with the new task's settings
                              // The useEffect will handle this once the task details are loaded
                            }}
                            onBlur={field.handleBlur}
                            className="w-full"
                          />
                        </field.FormControl>
                        <field.FormDescription>
                          Select the task you worked on.
                        </field.FormDescription>
                        <field.FormMessage />
                      </field.FormItem>
                    )}
                  </form.AppField>

                  <form.AppField name="startTimestamp">
                    {(field) => (
                      <field.FormItem>
                        <field.FormLabel>Start Time</field.FormLabel>
                        <field.FormControl>
                          <DatetimeInput
                            ref={startTimeRef}
                            format={dateTimeFormat}
                            value={field.state.value}
                            onChange={onStartTimeChanged}
                            onBlur={field.handleBlur}
                          />
                        </field.FormControl>
                        <field.FormDescription>
                          When did you start working on this task?
                        </field.FormDescription>
                        <field.FormMessage />
                      </field.FormItem>
                    )}
                  </form.AppField>

                  <form.AppField name="endTimestamp">
                    {(field) => (
                      <field.FormItem>
                        <field.FormLabel>End Time</field.FormLabel>
                        <field.FormControl>
                          {field.state.value ? (
                            <div className="flex gap-2">
                              <DatetimeInput
                                ref={endTimeRef}
                                format={[
                                  ['days', 'months', 'years'],
                                  ['hours', 'minutes'],
                                ]}
                                value={field.state.value}
                                onChange={onEndTimeChanged}
                                onBlur={field.handleBlur}
                              />
                              <Button
                                type="button"
                                onClick={onClearEndTimestamp}
                              >
                                Clear
                              </Button>
                            </div>
                          ) : (
                            <Button
                              ref={endButtonRef}
                              type="button"
                              onClick={(e) => {
                                e.preventDefault
                                field.handleChange(Date.now())
                              }}
                            >
                              Set now
                            </Button>
                          )}
                        </field.FormControl>
                        <field.FormDescription>
                          When did you finish working on this task?
                        </field.FormDescription>
                        <field.FormMessage />
                      </field.FormItem>
                    )}
                  </form.AppField>

                  <form.AppField name="comment">
                    {(field) => (
                      <field.FormItem>
                        <field.FormLabel>Comment</field.FormLabel>
                        <field.FormControl>
                          <Input
                            ref={commentRef}
                            placeholder="Comment"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            onBlur={field.handleBlur}
                          />
                        </field.FormControl>
                        <field.FormDescription>
                          Add any notes about this time record.
                        </field.FormDescription>
                        <field.FormMessage />
                      </field.FormItem>
                    )}
                  </form.AppField>

                  {/* Display and manage linked task catalogs */}
                  <TaskCatalogLinker
                    timeRecord={timeRecord}
                    onLinkedTaskCatalogsChange={() => {
                      // This will trigger a re-render of the LinkedTaskCatalogs component
                      // since it uses its own query
                    }}
                  />
                </div>

                <DialogFooter>
                  <div className="flex w-full justify-between">
                    <div>
                      {timeRecord && onDelete && (
                        <Button
                          type="button"
                          variant="destructive"
                          onClick={handleDeleteClick}
                        >
                          Delete
                        </Button>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button type="button" variant="outline" onClick={onClose}>
                        Cancel
                      </Button>
                      <form.Subscribe
                        selector={(state) => [
                          state.canSubmit,
                          state.isSubmitting,
                        ]}
                      >
                        {([canSubmit, isSubmitting]) => (
                          <Button
                            type="submit"
                            disabled={!canSubmit || isSubmitting}
                          >
                            {isSubmitting
                              ? 'Saving...'
                              : timeRecord
                                ? 'Update'
                                : 'Create'}
                          </Button>
                        )}
                      </form.Subscribe>
                    </div>
                  </div>
                </DialogFooter>
              </form>
            </form.AppForm>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <ConfirmationDialog
          isOpen={isDeleteConfirmOpen}
          onOpenChange={setIsDeleteConfirmOpen}
          onConfirm={handleConfirmDelete}
          title="Confirm Deletion"
          description="Are you sure you want to delete this time record? This action cannot be undone."
          confirmText="Delete"
        />
      </>
    )
  }
)
interface TimeNormalizationConfig {
  type: TimeNormalizationType
  config: string
}

function lookupTimeNormalizationConfig(
  taskDetails: TimeRecord['task'] | undefined
): TimeNormalizationConfig | undefined {
  const project = taskDetails?.project

  if (!project) {
    return undefined
  }

  if (project.timeNormalizationType) {
    return {
      type: project.timeNormalizationType as TimeNormalizationType,
      config: project.timeNormalizationConfig ?? '',
    }
  }
  if (project.customer?.timeNormalizationType) {
    return {
      type: project.customer.timeNormalizationType as TimeNormalizationType,
      config: project.customer.timeNormalizationConfig ?? '',
    }
  }
  return undefined
}

// Format timestamp for input fields in local timezone
function formatDateTimeForInput(timestamp: number) {
  const date = new Date(timestamp)
  // Format as YYYY-MM-DDThh:mm in local timezone
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day}T${hours}:${minutes}`
}

const ONE_HOUR_MS = 60 * 60 * 1000

interface ResolveResult {
  startTimestamp: number
  endTimestamp: number | null
}

function resolveStartEndTimestamp(
  timeRecord: TimeRecord | null,
  date: string | undefined
): ResolveResult {
  if (timeRecord) {
    return {
      startTimestamp: timeRecord.startTimestamp,
      endTimestamp: timeRecord.endTimestamp,
    }
  }
  if (date) {
    const startTimestamp = new Date(date).getTime()
    // check if the date is today
    if (isToday(startTimestamp)) {
      return {
        startTimestamp,
        endTimestamp: null,
      }
    }
    const endTimestamp = startTimestamp + ONE_HOUR_MS
    return {
      startTimestamp,
      endTimestamp,
    }
  }
  const startTimestamp = Date.now()

  return {
    startTimestamp,
    endTimestamp: null,
  }
}

function isToday(timestamp: number) {
  const today = new Date()
  const date = new Date(timestamp)
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  )
}
