import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { ActivityIcon, EyeIcon, MonitorIcon } from 'lucide-react'
import {
  type RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useRefify } from '~/lib/hooks/use-refify'
import { getIdleTime, hasBeenIdleFor, supportsInvoke } from '~/lib/idle-time'
import {
  type AppState,
  idleTimerExpired,
  makeCurrentClientActive,
} from '~/lib/model/app-state'
import { cn } from '~/lib/utils'
import { formatDateFromMs, formatTime } from '~/lib/utils/date-time'
import { clientId } from '~/modules/state/active-client'
import {
  formatDateTimeFromTimestamp,
  formatOptDateTimeFromTimestamp,
} from '~/utils/date'
import { ForegroundChecker } from './ForegroundChecker'
import { IdleTimeoutDialog } from './IdleTimeoutDialog'

const IDLE_CHECK_INTERVAL = 2000 // Check every 10 seconds
const IDLE_THRESHOLD = 300000 // 5 minute in milliseconds
const TIME_SKIP_THRESHOLD = 60000 // 1 minute in milliseconds

const invokeSupported = supportsInvoke()
/**
 * Status indicator component that displays an icon with a colored background
 */
function StatusIndicator({
  active,
  icon: Icon,
  label,
  className,
}: {
  active: boolean
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
  label: string
  className?: string
}) {
  return (
    <div
      className={cn(
        'flex items-center gap-2 px-2 py-1 rounded-md text-sm',
        active
          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
          : 'bg-gray-100 dark:bg-gray-800/50 text-gray-500 dark:text-gray-400',
        className
      )}
      title={`${label}: ${active ? 'Active' : 'Inactive'}`}
    >
      <Icon className="h-4 w-4" />
      <span>{label}</span>
    </div>
  )
}

/**
 * Component that monitors user idle time and updates app state when idle
 * threshold is reached while a timer is running.
 */
export function IdleTimeMonitor() {
  const z = useZero<Schema>()
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [isIdleForSecs, setIsIdleForSecs] = useState(0)

  const [appState, appStateDetails] = useQuery(
    z.query.appState
      .where('id', '=', z.userID)
      .related('runningTimer', (q) =>
        q.related('timerecord', (q) =>
          q.related('task', (q) => q.related('project'))
        )
      )
      .one(),
    {
      ttl: 'forever',
    }
  )

  const isActiveClient = appState?.activeClientId === clientId

  const appStateWithRunningTimer = useMemo(() => {
    const runningTimer = appState?.runningTimer
    if (!runningTimer) {
      return appState
    }
    return {
      ...appState,
      runningTimer: {
        ...runningTimer,
        startTimestamp: `${formatDateFromMs(runningTimer.startTimestamp)} ${formatTime(runningTimer.startTimestamp)}`,
        endTimestamp: formatOptDateTimeFromTimestamp(
          runningTimer.endTimestamp ?? undefined
        ),
      },
    }
  }, [appState])

  const appStateComplete = appStateDetails.type === 'complete'

  const runningTimer =
    appStateDetails.type === 'complete' ? appState?.runningTimer : undefined

  const timestampOfLastCheckRef = useRef(Date.now())
  const showingTimeoutMessage =
    appStateDetails.type === 'complete' && appState?.showingTimeoutMessage

  const timestampOfIdleTimeStart =
    appStateDetails.type === 'complete'
      ? appState?.timestampOfIdleTimeStart
      : undefined

  // Only monitor idle time if Tauri invoke is supported and there's a running timer
  const shouldMonitor =
    invokeSupported &&
    runningTimer?.status === 'running' &&
    !showingTimeoutMessage &&
    isActiveClient

  // Start or stop monitoring based on timer state
  useEffect(() => {
    setIsMonitoring(shouldMonitor)
  }, [shouldMonitor])

  // Monitor idle time
  useEffect(() => {
    if (!isMonitoring) {
      timestampOfLastCheckRef.current = 0
      return
    }

    const checkIdleTime = async () => {
      try {
        const now = Date.now()
        const lastTimestampOfCheck = timestampOfLastCheckRef.current
        timestampOfLastCheckRef.current = now
        const isIdle = await hasBeenIdleFor(IDLE_THRESHOLD)
        const idleTime = await getIdleTime()
        setIsIdleForSecs(idleTime / 1000)

        const timeSkipped =
          lastTimestampOfCheck > 0
            ? now - lastTimestampOfCheck > TIME_SKIP_THRESHOLD
            : false
        if (timeSkipped) {
          console.log(
            'Time skipped by ',
            (now - lastTimestampOfCheck) / 1000,
            's, resetting idle time',
            'last timestamp of check:',
            formatDateTimeFromTimestamp(lastTimestampOfCheck)
          )
        }

        if (isIdle) {
          console.log('User is idle for', idleTime / 1000, 'seconds', {
            showingTimeoutMessage,
            timestampOfIdleTimeStart: formatOptDateTimeFromTimestamp(
              timestampOfIdleTimeStart ?? undefined
            ),
          })
        }
        if ((timeSkipped || isIdle) && !showingTimeoutMessage) {
          console.log(
            'User has been idle for more than',
            IDLE_THRESHOLD / 1000,
            'seconds'
          )
          await idleTimerExpired(z, lastTimestampOfCheck)
        }
      } catch (error) {
        console.error('Error checking idle time:', error)
      }
    }

    // Initial check
    checkIdleTime()

    // Set up interval for checking idle time
    const intervalId = setInterval(checkIdleTime, IDLE_CHECK_INTERVAL)

    return () => clearInterval(intervalId)
  }, [isMonitoring, showingTimeoutMessage, timestampOfIdleTimeStart, z])

  const zRef = useRefify(z)
  const onForeground = useIsForeground(appStateComplete, appState, zRef)
  const foregroundCheckerEnabled =
    invokeSupported && (isIdleForSecs < 5 || !isMonitoring)

  const timeSinceLastIdleCheckInSecs =
    timestampOfLastCheckRef.current > 0
      ? (Date.now() - timestampOfLastCheckRef.current) / 1000
      : 0
  const timestampOfLastCheck = new Date(
    timestampOfLastCheckRef.current
  ).toISOString()

  const runningTimerDescription = useMemo(() => {
    if (!runningTimer || !runningTimer.timerecord) {
      return undefined
    }
    const timeRecord = runningTimer.timerecord
    return `Start Time: ${formatDateTimeFromTimestamp(runningTimer.startTimestamp)}, Comment: ${timeRecord.comment ?? 'no comment yet'}, Task: ${timeRecord.task?.name}, Project: ${timeRecord.task?.project?.name})`
  }, [runningTimer])
  return (
    <div className="flex flex-col gap-2">
      <ForegroundChecker
        onForeground={onForeground}
        enabled={foregroundCheckerEnabled}
      />

      <div className="flex flex-wrap gap-2 items-center">
        <StatusIndicator
          active={isMonitoring}
          icon={ActivityIcon}
          label="Idle Monitor"
        />

        <StatusIndicator
          active={foregroundCheckerEnabled}
          icon={EyeIcon}
          label="Foreground Check"
        />

        <StatusIndicator
          active={isActiveClient}
          icon={MonitorIcon}
          label="Active Client"
        />

        {isMonitoring && (
          <div className="text-sm text-muted-foreground">
            Idle for: {isIdleForSecs.toFixed(1)}s
          </div>
        )}
        <pre>
          last idle check {timeSinceLastIdleCheckInSecs.toFixed(1)}s ago (
          {timestampOfLastCheck})
        </pre>
      </div>

      <details className="text-xs">
        <summary className="cursor-pointer hover:text-primary">
          App State
        </summary>
        <pre className="text-xs mt-1 p-2 bg-muted/50 rounded overflow-auto max-h-40">
          {JSON.stringify(appStateWithRunningTimer, null, 2)}
        </pre>
      </details>

      {showingTimeoutMessage && (
        <IdleTimeoutDialog
          runningTimerDescription={runningTimerDescription}
          runningTimerId={runningTimer?.id ?? ''}
          timestampOfIdleTimeStart={
            typeof timestampOfIdleTimeStart === 'number'
              ? timestampOfIdleTimeStart
              : 0
          }
          z={z}
        />
      )}
    </div>
  )
}

function useIsForeground(
  appStateComplete: boolean,
  appStateResult: AppState | undefined,
  zRef: RefObject<Zero<Schema>>
) {
  const appStateRef = useRefify(appStateResult)

  // useEffect(() => {
  //   if (
  //     appStateComplete &&
  //     appStateRef.current &&
  //     !initialForegroundDoneRef.current
  //   ) {
  //     setInitialForegroundDone(true)
  //     console.log('checking foreground', appStateRef.current)
  //     if (appStateRef.current.activeClientId !== clientId) {
  //       console.log('active client id changed, updating app state')
  //       makeCurrentClientActive(zRef.current).catch((error) => {
  //         console.error('Error making current client active:', error)
  //       })
  //     }
  //   }
  // }, [appStateComplete, appStateRef, zRef, initialForegroundDoneRef])

  const onForeground = useCallback(() => {
    if (appStateComplete && appStateRef.current?.activeClientId !== clientId) {
      console.log('foreground')
      makeCurrentClientActive(zRef.current).catch((error) => {
        console.error('Error making current client active:', error)
      })
    }
  }, [appStateComplete, appStateRef, zRef])

  return onForeground
}
