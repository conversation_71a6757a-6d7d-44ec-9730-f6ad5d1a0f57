import type { Schema, Task, TaskCatalog } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import { DataTable } from '~/components/datatable'
import { TaskCatalogEditor } from '~/components/task-catalog-editor'
import { TaskCatalogPicker } from '~/components/task-catalog-picker'
import { TaskEditor } from '~/components/task-editor'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { useTaskManagement } from '~/modules/tasks/task-hooks'

export const Route = createFileRoute('/_auth/admin/tasks/$taskId')({
  component: TaskDetailPage,
})

function TaskDetailPage() {
  const { taskId } = Route.useParams()
  const z = useZero<Schema>()
  const navigate = useNavigate()
  const [selectedTaskCatalogId, setSelectedTaskCatalogId] = useState<
    string | null
  >(null)
  const [isTaskCatalogEditorOpen, setIsTaskCatalogEditorOpen] = useState(false)
  const [isLinkTaskCatalogDialogOpen, setIsLinkTaskCatalogDialogOpen] =
    useState(false)
  const [selectedTaskCatalogToLink, setSelectedTaskCatalogToLink] = useState<
    string | null
  >(null)

  // Fetch the task
  const [taskResult = null] = useQuery(
    z.query.tasks
      .where('id', '=', taskId)
      .related('project', (q) => q.related('customer'))
      .related('taskToTaskCatalogs', (q) =>
        q.related('taskCatalog', (q) => q.related('projectCatalog'))
      )
      .one(),
    { ttl: 'forever' }
  )

  const task = taskResult ?? null
  const linkedTaskCatalogsRelations = task?.taskToTaskCatalogs ?? []

  // Extract task catalogs from the relations
  const linkedTaskCatalogs = useMemo(() => {
    return linkedTaskCatalogsRelations
      .filter((relation) => relation.taskCatalog)
      .map(
        (relation) =>
          relation.taskCatalog as TaskCatalog & {
            projectCatalog?: { name: string } | null
          }
      )
  }, [linkedTaskCatalogsRelations])

  // Get the selected task catalog
  const selectedTaskCatalog =
    linkedTaskCatalogs.find(
      (catalog) => catalog.id === selectedTaskCatalogId
    ) || null

  // Handle row click
  const handleRowClick = (taskCatalog: TaskCatalog) => {
    setSelectedTaskCatalogId(taskCatalog.id)
  }

  // Handle row double click
  const handleRowDoubleClick = (taskCatalog: TaskCatalog) => {
    setSelectedTaskCatalogId(taskCatalog.id)
    setIsTaskCatalogEditorOpen(true)
  }

  // Handle add new task catalog
  const handleAddNewTaskCatalog = () => {
    setSelectedTaskCatalogId(null)
    setIsTaskCatalogEditorOpen(true)
  }

  // Handle link existing task catalog
  const handleLinkExistingTaskCatalog = () => {
    setSelectedTaskCatalogToLink(null)
    setIsLinkTaskCatalogDialogOpen(true)
  }

  const [editorOpen, setEditorOpen] = useState(false)
  const handleEditTask = () => {
    setEditorOpen(true)
  }
  // Handle link task catalog
  const handleLinkTaskCatalog = async () => {
    if (!selectedTaskCatalogToLink) return

    try {
      // Check if already linked
      const isAlreadyLinked = linkedTaskCatalogs.some(
        (catalog) => catalog.id === selectedTaskCatalogToLink
      )

      if (!isAlreadyLinked) {
        // Create the link
        await z.mutate.taskToTaskCatalogs.insert({
          taskId: taskId,
          taskCatalogId: selectedTaskCatalogToLink,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }

      setIsLinkTaskCatalogDialogOpen(false)
    } catch (error) {
      console.error('Error linking task catalog:', error)
    }
  }

  // Handle unlink task catalog
  const handleUnlinkTaskCatalog = async (taskCatalogId: string) => {
    try {
      // Find the relation
      const relation = linkedTaskCatalogsRelations.find(
        (rel) => rel.taskCatalog?.id === taskCatalogId
      )

      if (relation) {
        // Delete the relation
        await z.mutate.taskToTaskCatalogs.delete({
          taskCatalogId: relation.taskCatalogId,
          taskId: relation.taskId,
        })
      }
    } catch (error) {
      console.error('Error unlinking task catalog:', error)
    }
  }

  // Handle save task catalog
  const handleSaveTaskCatalog = async (taskCatalog: TaskCatalog) => {
    const alreadyExists = linkedTaskCatalogs.some(
      (t) => t.id === taskCatalog.id
    )
    try {
      await z.mutateBatch(async (tx) => {
        const taskCatalogId = taskCatalog.id || uuidv7()
        if (alreadyExists) {
          // Update existing task catalog
          await tx.taskCatalogs.update({
            id: taskCatalogId,
            name: taskCatalog.name,
            key: taskCatalog.key,
            status: taskCatalog.status,
            remoteId: taskCatalog.remoteId,
            remoteUrl: taskCatalog.remoteUrl,
            projectCatalogId: taskCatalog.projectCatalogId,
            pinned: taskCatalog.pinned,
            updatedAt: Date.now(),
            updatedBy: z.userID,
          })
        } else {
          // Create new task catalog

          await tx.taskCatalogs.insert({
            ...taskCatalog,
            id: taskCatalogId,
            lastUsed: null,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            createdBy: z.userID,
            updatedBy: z.userID,
          })
        }

        // Create the link
        await tx.taskToTaskCatalogs.insert({
          taskId: taskId,
          taskCatalogId: taskCatalogId,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      })

      setIsTaskCatalogEditorOpen(false)
    } catch (error) {
      console.error('Error saving task catalog:', error)
    }
  }

  // Handle delete task catalog
  const handleDeleteTaskCatalog = async (taskCatalog: TaskCatalog) => {
    try {
      // Delete the task catalog
      await z.mutate.taskCatalogs.delete({
        id: taskCatalog.id,
      })

      setIsTaskCatalogEditorOpen(false)
    } catch (error) {
      console.error('Error deleting task catalog:', error)
    }
  }

  // Define columns for the task catalogs table
  const columns: ColumnDef<
    TaskCatalog & { projectCatalog?: { name: string } | null }
  >[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'key',
      header: 'Key',
    },
    {
      accessorKey: 'projectCatalog.name',
      header: 'Project Catalog',
      cell: ({ row }) => row.original.projectCatalog?.name || '-',
    },
    {
      accessorKey: 'status',
      header: 'Status',
    },
    {
      accessorKey: 'remoteId',
      header: 'Remote ID',
      cell: ({ row }) => row.original.remoteId || '-',
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <Button
          variant="destructive"
          size="sm"
          onClick={(e) => {
            e.stopPropagation() // Prevent row click
            handleUnlinkTaskCatalog(row.original.id)
          }}
        >
          Unlink
        </Button>
      ),
    },
  ]

  // Handle back button click
  const handleBackClick = () => {
    navigate({ to: '/admin/tasks', search: { projectId: task?.projectId } })
  }

  const tasks = useMemo(() => [task] as Task[], [task])
  const { handleSave } = useTaskManagement(z, tasks)

  if (!task) {
    return <div>Loading...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleBackClick}>
            Back
          </Button>
          <h1 className="text-2xl font-bold">{task?.name}</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleEditTask}>
            Edit Task
          </Button>
          <Button variant="outline" onClick={handleLinkExistingTaskCatalog}>
            Link Existing Task Catalog
          </Button>
          <Button onClick={handleAddNewTaskCatalog}>
            Add New Task Catalog
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 p-4 border rounded-md">
        <div>
          <p className="text-sm font-medium">Project:</p>
          <p>{task?.project?.name || '-'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Customer:</p>
          <p>{task?.project?.customer?.name || '-'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Status:</p>
          <p>{task?.status || '-'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Default Task:</p>
          <p>{task?.defaultTask ? 'Yes' : 'No'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Pinned:</p>
          <p>{task?.pinned ? 'Yes' : 'No'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Last Used:</p>
          <p>
            {task?.lastUsed ? new Date(task.lastUsed).toLocaleString() : '-'}
          </p>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-2">Linked Task Catalogs</h2>
        <div className="border rounded-md">
          <DataTable
            columns={columns}
            data={linkedTaskCatalogs}
            onRowClick={handleRowClick}
            onRowDoubleClick={handleRowDoubleClick}
            selectedRowId={selectedTaskCatalogId || undefined}
          />
        </div>
      </div>

      {isTaskCatalogEditorOpen && (
        <TaskCatalogEditor
          taskCatalog={selectedTaskCatalog}
          isOpen={isTaskCatalogEditorOpen}
          onClose={() => setIsTaskCatalogEditorOpen(false)}
          onSave={handleSaveTaskCatalog}
          onDelete={handleDeleteTaskCatalog}
        />
      )}

      {/* Link Existing Task Catalog Dialog */}
      {isLinkTaskCatalogDialogOpen && (
        <Dialog
          open={isLinkTaskCatalogDialogOpen}
          onOpenChange={(open) =>
            !open && setIsLinkTaskCatalogDialogOpen(false)
          }
        >
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Link Existing Task Catalog</DialogTitle>
              <DialogDescription>
                Select an existing task catalog to link to this task.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4 flex flex-row w-full max-w-full min-w-full">
              <TaskCatalogPicker
                className="w-full"
                value={selectedTaskCatalogToLink || undefined}
                onChange={setSelectedTaskCatalogToLink}
                display="combobox"
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsLinkTaskCatalogDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleLinkTaskCatalog}
                disabled={!selectedTaskCatalogToLink}
              >
                Link Task Catalog
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      {editorOpen && (
        <TaskEditor
          task={task}
          isOpen={true}
          onClose={() => setEditorOpen(false)}
          onSave={handleSave}
        />
      )}
    </div>
  )
}
