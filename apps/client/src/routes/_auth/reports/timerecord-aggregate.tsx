import { Button } from '@/components/ui/button'
import {} from '@/components/ui/popover'
import { useAppForm } from '@/components/ui/tanstack-form'
import { useStore } from '@tanstack/react-form'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useCallback, useMemo, useState } from 'react'
import { toast } from 'sonner'
import * as v from 'valibot'
import { DataTable } from '~/components/datatable'
import { DATE_DEFAULTS, DatetimeInput } from '~/components/date-time-input'
import { MultiCustomerPicker } from '~/components/multi-customer-picker'
import { ProjectPicker } from '~/components/project-picker'
import { TaskPicker } from '~/components/task-picker'
import {
  getCurrentMonthRange,
  getCurrentWeekRange,
  getCurrentYearRange,
  getPreviousMonthRange,
  getPreviousWeekRange,
  getPreviousYearRange,
  getTodayRange,
} from '~/lib/utils/date-ranges'
import { formatDurationFromMs } from '~/lib/utils/date-time'
import { useAuthCtx } from '~/modules/auth/use-auth'
import { calculateServerUrl } from '~/modules/urls/url-builder'

export const Route = createFileRoute('/_auth/reports/timerecord-aggregate')({
  component: TimerecordAggregatePage,
})

// Define the schema for the form using Valibot
const FormSchema = v.object({
  startDate: v.date('Start date is required'),
  endDate: v.date('End date is required'),
  customerIds: v.nullish(v.array(v.string())),
  projectId: v.nullish(v.string()),
  taskId: v.nullish(v.string()),
})

type FormSchemaInput = v.InferInput<typeof FormSchema>

// Define the type for the aggregated data
interface AggregatedTimeRecord {
  customerId: string
  customerName: string
  customerCurrency: string
  customerRate: number
  projectId: string
  projectName: string
  taskId: string
  taskName: string
  totalDuration: number
  durationInHours: number
  price: number
}

// Define the columns for the DataTable
const columns: ColumnDef<AggregatedTimeRecord>[] = [
  {
    accessorKey: 'customerName',
    header: 'Customer',
  },
  {
    accessorKey: 'projectName',
    header: 'Project',
  },
  {
    accessorKey: 'taskName',
    header: 'Task',
  },
  {
    accessorKey: 'durationInHours',
    header: 'Duration',
    cell: ({ row }) => row.original.durationInHours.toFixed(2),
  },
  {
    accessorKey: 'totalDuration',
    header: 'Total Duration',
    cell: ({ row }) => formatDurationFromMs(row.original.totalDuration),
  },
  {
    accessorKey: 'customerRate',
    header: 'Rate',
    cell: ({ row }) => {
      if (!row.original.customerRate) {
        return '-'
      }
      return `${row.original.customerCurrency} ${row.original.customerRate?.toFixed(2) ?? '-'}`
    },
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      if (!row.original.customerRate) {
        return '-'
      }
      return `${row.original.customerCurrency} ${row.original.price.toFixed(2)}`
    },
  },
]

function TimerecordAggregatePage() {
  const auth = useAuthCtx()
  const navigate = useNavigate()
  const [aggregatedData, setAggregatedData] = useState<AggregatedTimeRecord[]>(
    []
  )
  const [totalDuration, setTotalDuration] = useState<number>(0)
  const [isLoading, setIsLoading] = useState(false)
  const jwt = auth.jwt

  // Function to fetch aggregated data from the server
  const fetchAggregatedData = useCallback(
    async (values: FormSchemaInput) => {
      setIsLoading(true)
      try {
        // Get the JWT token from cookies or localStorage

        console.log('got JWT:', jwt)
        if (!jwt) {
          toast.error('Authentication Error')
          return
        }

        // Prepare the request body
        const requestBody: Record<string, unknown> = {
          startTimestamp: values.startDate.getTime(),
          endTimestamp: values.endDate.getTime() + 86400000, // Add one day to include the end date
        }

        // Add optional filters if provided
        if (values.customerIds && values.customerIds.length > 0) {
          requestBody.customerIds = values.customerIds
        }
        if (values.projectId) {
          requestBody.projectIds = [values.projectId]
        }
        if (values.taskId) {
          requestBody.taskIds = [values.taskId]
        }

        const url = calculateServerUrl('/api/timerecord-aggregate')
        console.log('sending request to server', url)
        // Make the API request
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${jwt}`,
          },
          body: JSON.stringify(requestBody),
        })

        console.log('response from server:', response)
        if (!response.ok) {
          throw new Error(`Error: ${response.status} ${response.statusText}`)
        }

        const result = await response.json()

        if (result.success) {
          console.log('Received aggregated data:', result.data)
          // Just set the data directly - we'll handle validation in the total calculation
          setAggregatedData(result.data)
          setTotalDuration(result.totalDuration)
        } else {
          throw new Error(result.message || 'Failed to fetch data')
        }
      } catch (error) {
        console.error('Error fetching aggregated data:', error)
        toast.error(
          error instanceof Error ? error.message : 'Failed to fetch data'
        )
      } finally {
        setIsLoading(false)
      }
    },
    [jwt]
  )

  const defaultValue: FormSchemaInput = useMemo(
    (): FormSchemaInput => ({
      startDate: new Date(new Date().setDate(1)), // First day of current month
      endDate: new Date(),
      customerIds: [],
      projectId: null,
      taskId: null,
    }),
    []
  )

  // Create form with TanStack Form
  const form = useAppForm({
    defaultValues: defaultValue,
    onSubmit: async ({ value }) => {
      await fetchAggregatedData(value)
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: FormSchema,
    },
  })

  // Handle row click to navigate to details page
  const handleRowClick = useCallback(
    (row: AggregatedTimeRecord) => {
      // Navigate to the details page with the appropriate filters
      navigate({
        to: '/reports/timerecord-details',
        search: {
          startTimestamp: form.getFieldValue('startDate').getTime(),
          endTimestamp: form.getFieldValue('endDate').getTime() + 86400000, // Add one day to include the end date
          customerId: row.customerId,
          projectId: row.projectId,
          taskId: row.taskId,
          customerName: row.customerName,
          projectName: row.projectName,
          taskName: row.taskName,
        },
      })
    },
    [navigate, form]
  )

  const formValues = useStore(form.store, (state) => state.values)
  console.log('form values', formValues)

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Timerecord Aggregation Report</h1>

      <form.AppForm>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            form.handleSubmit()
          }}
          className="space-y-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Start Date */}
            <form.AppField name="startDate">
              {(field) => (
                <field.FormItem>
                  <field.FormLabel>Start Date</field.FormLabel>
                  <field.FormControl>
                    <DatetimeInput
                      name={field.name}
                      format={DATE_DEFAULTS}
                      value={field.state.value}
                      onChange={(e) => e && field.handleChange(e)}
                    />
                  </field.FormControl>
                  <field.FormDescription>
                    Select the start date for the report period.
                  </field.FormDescription>
                  <field.FormMessage />
                </field.FormItem>
              )}
            </form.AppField>

            {/* End Date */}
            <form.AppField name="endDate">
              {(field) => (
                <field.FormItem>
                  <field.FormLabel>End Date</field.FormLabel>
                  <field.FormControl>
                    <DatetimeInput
                      format={DATE_DEFAULTS}
                      name={field.name}
                      value={field.state.value}
                      onChange={(e) => e && field.handleChange(e)}
                    />
                    {/* <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.state.value && 'text-muted-foreground'
                          )}
                        >
                          {field.state.value ? (
                            field.state.value.toLocaleDateString()
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.state.value}
                          onSelect={(date) => date && field.handleChange(date)}
                          disabled={(date) =>
                            date > new Date() ||
                            date < form.getFieldValue('startDate')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover> */}
                  </field.FormControl>
                  <field.FormDescription>
                    Select the end date for the report period.
                  </field.FormDescription>
                  <field.FormMessage />
                </field.FormItem>
              )}
            </form.AppField>

            {/* Customer Picker */}
            <form.AppField name="customerIds">
              {(field) => (
                <field.FormItem>
                  <field.FormLabel>Customers</field.FormLabel>
                  <field.FormControl>
                    <MultiCustomerPicker
                      values={field.state.value ?? []}
                      onChange={(values) => field.handleChange(values)}
                      onBlur={field.handleBlur}
                      placeholder="All Customers"
                      allowClear
                    />
                  </field.FormControl>
                  <field.FormDescription>
                    Filter by customers (optional). Select multiple customers to
                    include them in the report.
                  </field.FormDescription>
                  <field.FormMessage />
                </field.FormItem>
              )}
            </form.AppField>

            {/* Project Picker */}
            <form.AppField name="projectId">
              {(field) => (
                <field.FormItem>
                  <field.FormLabel>Project</field.FormLabel>
                  <field.FormControl>
                    <ProjectPicker
                      value={field.state.value || undefined}
                      onChange={(value) => field.handleChange(value)}
                      onBlur={field.handleBlur}
                    />
                  </field.FormControl>
                  <field.FormDescription>
                    Filter by project (optional).
                  </field.FormDescription>
                  <field.FormMessage />
                </field.FormItem>
              )}
            </form.AppField>

            {/* Task Picker */}
            <form.AppField name="taskId">
              {(field) => (
                <field.FormItem>
                  <field.FormLabel>Task</field.FormLabel>
                  <field.FormControl>
                    <TaskPicker
                      value={field.state.value || undefined}
                      onChange={(value) => field.handleChange(value)}
                      onBlur={field.handleBlur}
                    />
                  </field.FormControl>
                  <field.FormDescription>
                    Filter by task (optional).
                  </field.FormDescription>
                  <field.FormMessage />
                </field.FormItem>
              )}
            </form.AppField>
          </div>

          {/* Date Range Quick Select Buttons */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getTodayRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              Today
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getCurrentWeekRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              This Week
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getPreviousWeekRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              Last Week
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getCurrentMonthRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              This Month
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getPreviousMonthRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              Last Month
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getCurrentYearRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              This Year
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const { startDate, endDate } = getPreviousYearRange()
                form.setFieldValue('startDate', startDate)
                form.setFieldValue('endDate', endDate)
              }}
            >
              Last Year
            </Button>
          </div>

          <form.Subscribe
            selector={(state) => [state.canSubmit, state.isSubmitting]}
          >
            {([canSubmit, isSubmitting]) => (
              <Button
                type="submit"
                disabled={!canSubmit || isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? 'Loading...' : 'Generate Report'}
              </Button>
            )}
          </form.Subscribe>
        </form>
      </form.AppForm>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Results</h2>
        {aggregatedData.length > 0 ? (
          <>
            <DataTable
              columns={columns}
              data={aggregatedData}
              stickyHeaders
              onRowClick={handleRowClick}
            />
            <div className="mt-4 text-right font-medium">
              Total Duration: {formatDurationFromMs(totalDuration)}
            </div>
          </>
        ) : (
          <div className="text-center p-8 border rounded-md">
            {isLoading
              ? 'Loading data...'
              : 'No data to display. Generate a report to see results.'}
          </div>
        )}
      </div>
    </div>
  )
}
