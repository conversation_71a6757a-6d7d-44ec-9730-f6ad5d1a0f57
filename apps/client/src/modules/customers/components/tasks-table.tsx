import type { Schema, Task } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import type { ColumnDef } from '@tanstack/react-table'
import { type FC, useMemo, useRef } from 'react'
import { DataTable, type DataTableRef } from '~/components/datatable'
import type { TimeRecord } from '~/components/time-record-editor'

import { sortBy } from 'es-toolkit'
import { formatDurationFromMs } from '~/lib/utils/date-time'
import style from './customers-table.module.css'

interface TasksTableProps {
  selectedCustomerId?: string
  selectedProjectId?: string
  selectedTaskId?: string
  onTaskChange: (taskId: string | undefined) => void
  runningTimeRecordId?: string
  durationOfRunningTimeRecord?: number
  timerecords: TimeRecord[]
}

export const TasksTable: FC<TasksTableProps> = ({
  selectedProjectId,
  selectedTaskId,
  onTaskChange,
  timerecords,
  runningTimeRecordId,
  durationOfRunningTimeRecord,
}) => {
  const z = useZero<Schema>()
  const tableRef = useRef<DataTableRef>(null)

  const [tasks = []] = useQuery(
    z.query.tasks.where('projectId', '=', selectedProjectId ?? '').limit(100),
    {
      ttl: 'forever',
      enabled: !!selectedProjectId,
    }
  )

  // Calculate duration by task
  const durationByTask = useMemo(() => {
    const resultMap = new Map<string, number>()
    for (const record of timerecords) {
      const taskId = record.task?.id
      if (taskId) {
        const currentDuration = resultMap.get(taskId) ?? 0
        const duration = calculateRecordDuration(
          record,
          runningTimeRecordId,
          durationOfRunningTimeRecord
        )
        resultMap.set(taskId, currentDuration + duration)
      }
    }
    return resultMap
  }, [timerecords, durationOfRunningTimeRecord, runningTimeRecordId])

  // Sort tasks by duration
  const sortedTasks = useMemo(() => {
    return sortBy(tasks, [
      (task) => durationByTask.get(task.id) ?? 0,
    ]).toReversed()
  }, [tasks, durationByTask])

  // Find task ID of running timerecord
  const taskIdOfRunningTimerecord = useMemo(() => {
    const timeRecord = timerecords.find(
      (record) => record.id === runningTimeRecordId
    )
    return timeRecord?.task?.id
  }, [timerecords, runningTimeRecordId])

  // Define columns for the DataTable
  const columns = useMemo<ColumnDef<Task>[]>(() => {
    return [
      {
        accessorKey: 'name',
        header: 'Task',
        cell: ({ row }) => row.original.name,
      },
      {
        accessorKey: 'duration',
        header: 'Duration',
        cell: ({ row }) => {
          const duration = durationByTask.get(row.original.id) ?? 0
          return formatDurationFromMs(duration)
        },
      },
    ]
  }, [durationByTask])

  // Handle row click
  const handleRowClick = (task: Task) => {
    onTaskChange(task.id)
  }

  // Highlight running timerecord's task
  const highlightedRowId = taskIdOfRunningTimerecord

  return (
    <div className={`${style.customersTable} mb-2`}>
      <DataTable
        ref={tableRef}
        columns={columns}
        data={sortedTasks}
        onRowClick={handleRowClick}
        getRowId={(task: Task) => task.id}
        selectedRowId={selectedTaskId}
        highlightedRowId={highlightedRowId}
        stickyHeaders={true}
      />
    </div>
  )
}

function calculateRecordDuration(
  record: TimeRecord,
  runningTimeRecordId: string | undefined,
  durationOfRunningTimeRecord: number | undefined
) {
  if (
    record.id === runningTimeRecordId &&
    durationOfRunningTimeRecord !== undefined
  ) {
    return durationOfRunningTimeRecord
  }
  if (!record.endTimestamp) {
    return 0
  }
  return record.endTimestamp - record.startTimestamp
}
