import type { Project, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import type { ColumnDef } from '@tanstack/react-table'
import { type FC, useMemo, useRef } from 'react'
import { DataTable, type DataTableRef } from '~/components/datatable'
import type { TimeRecord } from '~/components/time-record-editor'

import { sortBy } from 'es-toolkit'
import { formatDurationFromMs } from '~/lib/utils/date-time'
import style from './customers-table.module.css'

interface ProjectsTableProps {
  selectedCustomerId?: string
  selectedProjectId?: string
  onProjectChange: (projectId: string | undefined) => void
  runningTimeRecordId?: string
  durationOfRunningTimeRecord?: number
  timerecords: TimeRecord[]
}

export const ProjectsTable: FC<ProjectsTableProps> = ({
  selectedCustomerId,
  selectedProjectId,
  onProjectChange,
  timerecords,
  runningTimeRecordId,
  durationOfRunningTimeRecord,
}) => {
  const z = useZero<Schema>()
  const tableRef = useRef<DataTableRef>(null)

  const [projects = []] = useQuery(
    z.query.projects
      .where('customerId', '=', selectedCustomerId ?? '')
      .limit(100),
    {
      ttl: 'forever',
      enabled: !!selectedCustomerId,
    }
  )

  // Calculate duration by project
  const durationByProject = useMemo(() => {
    const resultMap = new Map<string, number>()
    for (const record of timerecords) {
      const projectId = record.task?.project?.id
      if (projectId) {
        const currentDuration = resultMap.get(projectId) ?? 0
        const duration = calculateRecordDuration(
          record,
          runningTimeRecordId,
          durationOfRunningTimeRecord
        )
        resultMap.set(projectId, currentDuration + duration)
      }
    }
    return resultMap
  }, [timerecords, durationOfRunningTimeRecord, runningTimeRecordId])

  // Sort projects by duration
  const sortedProjects = useMemo(() => {
    return sortBy(projects, [
      (project) => durationByProject.get(project.id) ?? 0,
    ]).toReversed()
  }, [projects, durationByProject])

  // Find project ID of running timerecord
  const projectIdOfRunningTimerecord = useMemo(() => {
    const timeRecord = timerecords.find(
      (record) => record.id === runningTimeRecordId
    )
    return timeRecord?.task?.project?.id
  }, [timerecords, runningTimeRecordId])

  // Define columns for the DataTable
  const columns = useMemo<ColumnDef<Project>[]>(() => {
    return [
      {
        accessorKey: 'name',
        header: 'Project',
        cell: ({ row }) => row.original.name,
      },
      {
        accessorKey: 'duration',
        header: 'Duration',
        cell: ({ row }) => {
          const duration = durationByProject.get(row.original.id) ?? 0
          return formatDurationFromMs(duration)
        },
      },
    ]
  }, [durationByProject])

  // Handle row click
  const handleRowClick = (project: Project) => {
    onProjectChange(project.id)
  }

  // Highlight running timerecord's project
  const highlightedRowId = projectIdOfRunningTimerecord

  return (
    <div className={`${style.customersTable} mb-2`}>
      <DataTable
        ref={tableRef}
        columns={columns}
        data={sortedProjects}
        onRowClick={handleRowClick}
        getRowId={(project: Project) => project.id}
        selectedRowId={selectedProjectId}
        highlightedRowId={highlightedRowId}
        stickyHeaders={true}
      />
    </div>
  )
}

function calculateRecordDuration(
  record: TimeRecord,
  runningTimeRecordId: string | undefined,
  durationOfRunningTimeRecord: number | undefined
) {
  if (
    record.id === runningTimeRecordId &&
    durationOfRunningTimeRecord !== undefined
  ) {
    return durationOfRunningTimeRecord
  }
  if (!record.endTimestamp) {
    return 0
  }
  return record.endTimestamp - record.startTimestamp
}
