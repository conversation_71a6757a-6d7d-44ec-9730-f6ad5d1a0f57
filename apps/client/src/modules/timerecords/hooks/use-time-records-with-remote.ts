import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { useQuery } from '@rocicorp/zero/react'
import { useCallback, useEffect } from 'react'
import type { SlimTimeRecord } from '~/components/time-record-editor'
import { createTimeRecord, updateTimeRecord } from '~/lib/model/app-state'
import type { DbContext } from '~/lib/services/database-service'
import type { TimeRecordInput } from '../time-record-model'

interface UseTimeRecordsOptions {
  z: Zero<Schema>
  selectedStart: number
  selectedEnd: number
}

/**
 * Hook for fetching and managing time records
 * Includes integration with remote service adapters
 */
export function useTimeRecords({
  z,
  selectedStart,
  selectedEnd,
}: UseTimeRecordsOptions) {
  // Fetch time records for the selected date range
  const [timeRecords] = useQuery(
    z.query.timerecords
      .related('task', (q) =>
        q.related('project', (q) => q.related('customer'))
      )
      .where(({ cmp, and }) => {
        return and(
          cmp('startTimestamp', '>=', selectedStart),
          cmp('startTimestamp', '<=', selectedEnd)
        )
      })
      .orderBy('startTimestamp', 'asc')
      .limit(100),
    {
      ttl: 'forever',
    }
  )

  useEffect(() => {
    console.log('useTimeRecords: timerecords changed', timeRecords)
  }, [timeRecords])

  /**
   * Save a time record (create or update) and sync with remote service
   */
  const saveTimeRecord = useCallback(
    async (timeRecord: TimeRecordInput) => {
      try {
        // First save the time record locally
        if (timeRecords.some((record) => record.id === timeRecord.id)) {
          await updateTimeRecord(z, {
            id: timeRecord.id,
            taskId: timeRecord.taskId,
            startTimestamp: timeRecord.startTimestamp,
            endTimestamp: timeRecord.endTimestamp,
            comment: timeRecord.comment,
          })
        } else {
          await createTimeRecord(z, {
            id: timeRecord.id,
            taskId: timeRecord.taskId,
            startTimestamp: timeRecord.startTimestamp,
            endTimestamp: timeRecord.endTimestamp,
            comment: timeRecord.comment,
          })
        }
      } catch (error) {
        console.error('Error saving time record:', error)
      }
    },
    [timeRecords, z]
  )

  /**
   * Delete a time record and remove from remote service
   */
  const deleteTimeRecord = useCallback(
    async (timeRecord: SlimTimeRecord) => {
      try {
        // Use the database service to delete the time record and sync with remote service
        const { deleteTimeRecord } = await import('~/lib/model/app-state')
        const dbContext: DbContext = { z, tx: z.mutate }
        await deleteTimeRecord(dbContext, timeRecord.id)
      } catch (error) {
        console.error('Error deleting time record:', error)
      }
    },
    [z]
  )

  return {
    timeRecords,
    saveTimeRecord,
    deleteTimeRecord,
  }
}
