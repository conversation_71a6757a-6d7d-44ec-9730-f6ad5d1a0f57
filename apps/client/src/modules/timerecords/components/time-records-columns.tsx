import type { ColumnDef } from '@tanstack/react-table'
import type { TimeRecord } from '~/components/time-record-editor'
import type { TimerStruct } from '~/lib/model/app-state'
import {
  formatDateFromMs,
  formatDuration,
  formatDurationFromMs,
  formatTime,
} from '~/lib/utils/date-time'

/**
 * Create column definitions for the time records DataTable
 */
export function createTimeRecordColumns(
  runningTimer: TimerStruct | undefined,
  currentTime: number | undefined,
  totalDuration: number
): ColumnDef<TimeRecord>[] {
  const runningTimerTimerecordId = runningTimer?.worklogId

  return [
    {
      id: 'startDate',
      accessorKey: 'startTimestamp',
      header: 'Date',
      size: 60,
      enableResizing: false,
      cell: ({ row }) => formatDateFromMs(row.original.startTimestamp),
    },
    {
      accessorKey: 'startTimestamp',
      header: 'Start',
      size: 60,
      enableResizing: false,
      cell: ({ row }) => formatTime(row.original.startTimestamp),
    },
    {
      accessorKey: 'endTimestamp',
      header: 'End',
      size: 60,
      enableResizing: false,
      cell: ({ row }) => {
        const timeRecord = row.original
        const isRunningTimer = timeRecord.id === runningTimerTimerecordId

        return timeRecord.endTimestamp
          ? formatTime(timeRecord.endTimestamp)
          : isRunningTimer
            ? 'Running'
            : '-'
      },
    },
    {
      id: 'duration',
      header: 'Duration',
      size: 60,
      enableResizing: false,
      cell: ({ row }: { row: { original: TimeRecord } }) => {
        const timeRecord = row.original
        const isRunningTimer = timeRecord.id === runningTimerTimerecordId

        return isRunningTimer && currentTime
          ? formatDuration(timeRecord.startTimestamp, currentTime)
          : timeRecord.endTimestamp
            ? formatDuration(timeRecord.startTimestamp, timeRecord.endTimestamp)
            : '-'
      },
      footer:
        totalDuration > 0
          ? () => formatDurationFromMs(totalDuration)
          : undefined,
    },
    {
      id: 'project',
      header: 'Project',
      size: 200,
      minSize: 150,
      maxSize: 400,
      enableResizing: true,
      cell: ({ row }: { row: { original: TimeRecord } }) => {
        const timeRecord = row.original
        const projectText = `${timeRecord.task?.project?.name || '-'} / ${timeRecord.task?.name}`
        return (
          <div
            className="truncate overflow-hidden whitespace-nowrap"
            title={projectText}
          >
            {projectText}
          </div>
        )
      },
    },
    {
      accessorKey: 'comment',
      header: 'Comment',
      size: 200,
      minSize: 100,
      maxSize: 300,
      enableResizing: true,
      cell: ({ row }: { row: { original: TimeRecord } }) => {
        const text = row.original.comment || '-'
        return (
          <div
            className="truncate overflow-hidden whitespace-nowrap"
            title={text}
          >
            {text}
          </div>
        )
      },
    },
  ]
}
