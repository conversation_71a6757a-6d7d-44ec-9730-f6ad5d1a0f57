import { ThemeProvider } from '@/components/theme-provider'
import { schema } from '@ftt/shared'
import { Zero } from '@rocicorp/zero'
import { ZeroProvider } from '@rocicorp/zero/react'

import './styles.css'

import { RouterProvider, createRouter } from '@tanstack/react-router'
import { invariant } from 'es-toolkit'
import { type FC, StrictMode, useMemo } from 'react'
import ReactDOM from 'react-dom/client'
import { toast } from 'sonner'
import { StateInitializer } from './components/state/state-initializer.tsx'
import { Toaster } from './components/ui/sonner.tsx'
import { AuthProvider, useAuthCtx } from './modules/auth/use-auth.tsx'
import reportWebVitals from './reportWebVitals.ts'
// router stuff
import { routeTree } from './routeTree.gen'

// Create a new router instance
const router = createRouter({
  routeTree,
  context: {},
  defaultPreload: 'intent',
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPreloadStaleTime: 0,
})

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const InnerApp: FC = () => {
  const auth = useAuthCtx()
  const { jwt, isAuthenticated, user } = auth
  const userID = user?.id

  const z = useMemo(() => {
    if (!jwt) {
      return null
    }
    if (!userID) {
      return null
    }
    return new Zero({
      userID,
      auth: () => jwt,
      server: import.meta.env.VITE_PUBLIC_SERVER,
      schema,
      kvStore: 'idb',
      onError: (error) => {
        console.error('Zero error:', error)
        toast.error(`Something went wrong: ${error}`)
      },
    })
  }, [jwt, userID])

  if (isAuthenticated === 'loading') {
    return null
  }
  if (isAuthenticated === 'unauthenticated') {
    return null
  }
  invariant(jwt, 'No JWT')

  if (!z) {
    return null
  }

  return (
    <ZeroProvider zero={z}>
      <StateInitializer />
      <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
        <RouterProvider router={router} context={{ auth }} />
        <Toaster />
      </ThemeProvider>
    </ZeroProvider>
  )
}

const rootElement = document.getElementById('root')
if (rootElement && !rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <AuthProvider>
        <InnerApp />
      </AuthProvider>
    </StrictMode>
  )
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals(console.log)
