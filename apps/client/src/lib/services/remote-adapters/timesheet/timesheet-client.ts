import { isTauri } from '@tauri-apps/api/core'
import { fetch as fetchTauri } from '@tauri-apps/plugin-http'
import { invariant } from 'es-toolkit'
import * as v from 'valibot'
import type { RemoteServiceObject } from '../remote-adapter-types'

const TimecardSchema = v.object({
  timecardId: v.string(),
  employeeId: v.number(),
  editoriId: v.number(),
  projectTaskId: v.nullish(v.number()),
  startTime: v.string(),
  endTime: v.string(),
  date: v.string(),
  comments: v.nullish(v.string()),
  dateLastModified: v.string(),
  breaks: v.array(
    v.object({
      timecardId: v.string(),
      breakId: v.string(),
      startTime: v.string(),
      endTime: v.string(),
    })
  ),
  state: v.string(),
  interval: v.object({
    start: v.string(),
    end: v.string(),
  }),
})

const TimecardArraySchema = v.array(TimecardSchema)

export type TimecardData = v.InferOutput<typeof TimecardSchema>
export type TimecardArrayData = v.InferOutput<typeof TimecardArraySchema>

export async function getTimecard(
  date: string,
  remoteService: RemoteServiceObject
): Promise<TimecardArrayData> {
  const baseUrl = remoteService.remoteUrl
  // const baseUrl = 'http://localhost:9999'
  const url = `${baseUrl}/api/1.0/timecard?start=${date}&end=${date}`

  invariant(remoteService.remotePassword, 'No password')
  const parsed = await fetchAndParse(
    url,
    remoteService.remotePassword,
    TimecardArraySchema
  )
  return parsed
}

export async function fetchAndParse<
  const TSchema extends v.BaseSchema<unknown, unknown, v.BaseIssue<unknown>>,
>(url: string, jwt: string, schema: TSchema): Promise<v.InferOutput<TSchema>> {
  const customFetch = isTauri() ? fetchTauri : fetch
  const response = await customFetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${jwt}`,
    },
  })
  if (!response.ok) {
    throw new Error(`Failed to fetch timecard: ${response.statusText}`)
  }
  const parsed = v.parse(schema, await response.json())
  return parsed
}
