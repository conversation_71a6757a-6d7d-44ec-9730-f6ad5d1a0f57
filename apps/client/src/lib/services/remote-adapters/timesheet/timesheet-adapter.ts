import type { Schema, Timerecord } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { ServiceType } from '~/lib/model/service-type-model'
import { formatDateAsIso } from '~/utils/date'
import type {
  ParsingResult,
  ProjectCatalogObject,
  RemoteAdapter,
  RemoteServiceObject,
  RemoteServiceProvider,
  RemoteWorklog,
  TaskCatalogObject,
  WorklogObject,
} from '../remote-adapter-types'
import { type TimecardArrayData, getTimecard } from './timesheet-client'

export class TimesheetAdapter implements RemoteAdapter {
  serviceType = ServiceType.GOFORE_TIMESHEET
  needsImportForFoundProject = false

  async addWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    console.log('adding worklog')

    const dateOfWorklog = new Date(worklog.startTimestamp)
    const start = new Date(dateOfWorklog)
    start.setHours(0, 0, 0, 0)
    const end = new Date(dateOfWorklog)
    end.setHours(23, 59, 59, 999)

    console.log('find worklogs of the same day', {
      start: start.toISOString(),
      end: end.toISOString(),
    })

    // first find all worklogs which have a taskCatalog within the same remote service
    // and the same date (today)
    const worklogs = await z.query.timerecords
      .where('startTimestamp', '>=', start.getTime())
      .where('startTimestamp', '<=', end.getTime())
      .whereExists('timerecordToTaskCatalogs', (q) =>
        q.whereExists('taskCatalog', (q) =>
          q.whereExists('projectCatalog', (q) =>
            q.where('remoteServiceId', '=', remoteService.id)
          )
        )
      )

    const dateStr = formatDateAsIso(dateOfWorklog)
    const timecards = await getTimecard(dateStr, remoteService)
    const result = calculateTimecard(worklogs, timecards, worklog)
    console.log('result', result)
    return {} as { worklogId: string; timestamp: Date }
  }

  async getWorklog(
    _z: Zero<Schema>,
    _taskCatalog: TaskCatalogObject,
    _worklogId: string,
    _remoteService: RemoteServiceObject
  ): Promise<RemoteWorklog> {
    console.log('not implemented')
    return {} as RemoteWorklog
  }

  async updateWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _remoteWorklog: RemoteWorklog | null,
    _remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    console.log('not implemented')
    return {} as { worklogId: string; timestamp: Date }
  }

  async updateAndMoveWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _oldRemoteIssue: TaskCatalogObject,
    _remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    console.log('not implemented')
    return {} as { worklogId: string; timestamp: Date }
  }

  async deleteWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _remoteService: RemoteServiceObject
  ): Promise<number> {
    console.log('not implemented')
    return 0
  }

  async findAllProjects(
    _z: Zero<Schema>,
    _remoteService: RemoteServiceObject
  ): Promise<ProjectCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findTasksForProjectByKeyStartingWith(
    _z: Zero<Schema>,
    _keyPrefix: string,
    _projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findTasksForProjectBySearchText(
    _z: Zero<Schema>,
    _searchText: string,
    _projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findMostRecentTasksForProject(
    _z: Zero<Schema>,
    _projectCatalog: ProjectCatalogObject,
    _remoteService: RemoteServiceObject
  ): Promise<TaskCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findTaskByKey(
    _z: Zero<Schema>,
    _issueKey: string,
    _projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject | null> {
    console.log('not implemented')
    return null
  }

  async supportsUrl(
    _url: URL,
    _provider: RemoteServiceProvider
  ): Promise<boolean> {
    console.log('not implemented')
    return false
  }

  async parseIssueKeyFromUrl(
    _url: URL,
    _provider: RemoteServiceProvider
  ): Promise<ParsingResult | undefined> {
    console.log('not implemented')
    return undefined
  }

  async tryAuthenticate(
    _z: Zero<Schema>,
    _remoteService: RemoteServiceObject
  ): Promise<boolean> {
    console.log('not implemented')
    return false
  }
}

type TimecardResult = {
  start: string
  end: string
  breaks: { start: string; end: string }[]
}

export function calculateTimecard(
  worklogs: Timerecord[],
  timecards: TimecardArrayData,
  worklog: WorklogObject
): TimecardResult | undefined {
  console.log('worklog', JSON.stringify(worklog, null, 2))
  console.log('timecards', JSON.stringify(timecards, null, 2))
  console.log('worklogs of the same day', JSON.stringify(worklogs, null, 2))
  return undefined
}
