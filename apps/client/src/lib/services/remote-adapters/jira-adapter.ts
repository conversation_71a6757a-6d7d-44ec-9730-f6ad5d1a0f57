import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { invariant } from 'es-toolkit'
import { ServiceType } from '~/lib/model/service-type-model'
import { RemoteAdapterBase } from './remote-adapter-base'
import {
  type ParsingResult,
  type ProjectCatalogObject,
  ProtocolError,
  ProtocolErrorType,
  type RemoteServiceObject,
  type RemoteServiceProvider,
  type RemoteWorklog,
  type TaskCatalogObject,
  type WorklogObject,
} from './remote-adapter-types'

/**
 * Jira Cloud adapter implementation
 */
export class JiraCloudAdapter extends RemoteAdapterBase {
  serviceType = ServiceType.JIRA_CLOUD
  needsImportForFoundProject = true

  /**
   * Adds a new worklog to Jira
   */
  async addWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    // try {
    //   // Get the linked task catalog for this worklog
    //   const taskCatalog = await this.getPrimaryTaskCatalog(z, worklog.id)
    //   if (!taskCatalog) {
    //     throw new Error('No task catalog linked to this worklog')
    //   }

    //   // In a real implementation, this would make an API call to Jira
    //   // For now, we'll simulate a successful response
    //   const remoteWorklogId = `jira-worklog-${Date.now()}`
    //   const timestamp = new Date()

    //   // Update the worklog with the remote ID
    //   const uploadData = {
    //     remoteWorklogId,
    //     remoteIssueKey: taskCatalog.key,
    //     remoteServiceId: remoteService.id,
    //   }
    //   await this.updateWorklogUploadStatus(z, worklog.id, uploadData, true)

    //   return { worklogId: remoteWorklogId, timestamp }
    // } catch (error) {
    //   console.error('Error adding worklog to Jira:', error)

    //   // Update the worklog with the error
    //   const uploadData = {
    //     error: error instanceof Error ? error.message : String(error),
    //     remoteServiceId: remoteService.id,
    //   }
    //   await this.updateWorklogUploadStatus(z, worklog.id, uploadData, false)

    //   throw error
    // }
    throw new Error('not yet implemented')
  }

  /**
   * Gets a worklog from Jira
   */
  async getWorklog(
    _z: Zero<Schema>,
    taskCatalog: TaskCatalogObject,
    worklogId: string,
    remoteService: RemoteServiceObject
  ): Promise<RemoteWorklog> {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll simulate a response or throw an error

    if (!worklogId) {
      throw new ProtocolError(
        ProtocolErrorType.WORKLOG_NOT_FOUND,
        'Worklog ID is required'
      )
    }

    // Simulate a 10% chance of not finding the worklog
    if (Math.random() < 0.1) {
      throw new ProtocolError(
        ProtocolErrorType.WORKLOG_NOT_FOUND,
        `Worklog ${worklogId} not found in issue ${taskCatalog.key}`
      )
    }

    // Simulate a successful response
    const now = new Date()
    const startTime = new Date(now.getTime() - 3600000) // 1 hour ago
    const endTime = new Date(now)

    return {
      startTime,
      endTime,
      comment: 'Retrieved from Jira',
      updateAuthor: remoteService.remoteUser || 'Unknown',
      remoteWorklogId: worklogId,
      created: new Date(now.getTime() - 86400000), // 1 day ago
      modified: now,
    }
  }

  /**
   * Updates a worklog in Jira
   */
  async updateWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _remoteWorklog: RemoteWorklog | null,
    _remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    // try {
    //   // Get the linked task catalog for this worklog
    //   const taskCatalog = await this.getPrimaryTaskCatalog(z, worklog.id)
    //   if (!taskCatalog) {
    //     throw new Error('No task catalog linked to this worklog')
    //   }

    //   // Get the remote worklog ID from the upload data
    //   const uploadData = worklog.uploadData ?? {}
    //   const remoteWorklogId = uploadData[taskCatalog.id]?.remoteWorklogId

    //   if (!remoteWorklogId) {
    //     // If there's no remote worklog ID, create a new worklog instead
    //     return this.addWorklog(z, worklog, remoteService)
    //   }

    //   // In a real implementation, this would make an API call to Jira
    //   // For now, we'll simulate a successful response
    //   const timestamp = new Date()

    //   // Update the worklog with the remote ID
    //   const newUploadData: UploadData = {
    //     ...uploadData,
    //     [taskCatalog.id]: {
    //       remoteTimestamp: timestamp.getTime(),
    //       uploadTimestamp: Date.now(),
    //       taskCatalogId: taskCatalog.id,
    //       remoteWorklogId,
    //     },
    //   }
    //   await this.updateWorklogUploadStatus(z, worklog.id, newUploadData, true)

    //   return { worklogId: remoteWorklogId, timestamp }
    // } catch (error) {
    //   console.error('Error updating worklog in Jira:', error)

    //   // Update the worklog with the error
    //   const uploadData = {
    //     ...(worklog.uploadData || {}),
    //     error: error instanceof Error ? error.message : String(error),
    //     remoteServiceId: remoteService.id,
    //   }
    //   await this.updateWorklogUploadStatus(z, worklog.id, uploadData, false)

    //   throw error
    // }
    throw new Error('not yet implemented')
  }

  /**
   * Deletes a worklog from Jira
   */
  async deleteWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    _remoteService: RemoteServiceObject
  ): Promise<number> {
    try {
      // Get the remote worklog ID from the upload data
      const uploadData = worklog.uploadData || {}
      const remoteWorklogId = uploadData.remoteWorklogId

      if (!remoteWorklogId) {
        // If there's no remote worklog ID, nothing to delete
        return 404
      }

      // In a real implementation, this would make an API call to Jira
      // For now, we'll simulate a successful response

      // Update the worklog to remove the remote ID
      await this.updateWorklogUploadStatus(z, worklog.id, null, false)

      return 204 // No content (success)
    } catch (error) {
      console.error('Error deleting worklog from Jira:', error)
      throw error
    }
  }

  /**
   * Finds all projects in Jira
   */
  async findAllProjects(
    _z: Zero<Schema>,
    remoteService: RemoteServiceObject
  ): Promise<ProjectCatalogObject[]> {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll return some mock data
    return [
      {
        id: 'project-1',
        name: 'Project One',
        key: 'PROJ1',
        remoteId: 'jira-project-1',
        remoteUrl: `${remoteService.remoteUrl}/projects/PROJ1`,
        remoteServiceId: remoteService.id,
      },
      {
        id: 'project-2',
        name: 'Project Two',
        key: 'PROJ2',
        remoteId: 'jira-project-2',
        remoteUrl: `${remoteService.remoteUrl}/projects/PROJ2`,
        remoteServiceId: remoteService.id,
      },
    ]
  }

  /**
   * Finds tasks in Jira by key prefix
   */
  async findTasksForProjectByKeyStartingWith(
    _z: Zero<Schema>,
    _keyPrefix: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]> {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll return some mock data
    return [
      {
        id: 'task-1',
        name: 'Task One',
        key: `${projectCatalog.key}-1`,
        status: 'Open',
        remoteId: 'jira-task-1',
        remoteUrl: `${projectCatalog.remoteUrl}/browse/${projectCatalog.key}-1`,
        projectCatalogId: projectCatalog.id,
      },
      {
        id: 'task-2',
        name: 'Task Two',
        key: `${projectCatalog.key}-2`,
        status: 'In Progress',
        remoteId: 'jira-task-2',
        remoteUrl: `${projectCatalog.remoteUrl}/browse/${projectCatalog.key}-2`,
        projectCatalogId: projectCatalog.id,
      },
    ]
  }

  /**
   * Finds tasks in Jira by search text
   */
  async findTasksForProjectBySearchText(
    _z: Zero<Schema>,
    searchText: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]> {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll return some mock data
    return [
      {
        id: 'task-3',
        name: `Task with ${searchText}`,
        key: `${projectCatalog.key}-3`,
        status: 'Open',
        remoteId: 'jira-task-3',
        remoteUrl: `${projectCatalog.remoteUrl}/browse/${projectCatalog.key}-3`,
        projectCatalogId: projectCatalog.id,
      },
      {
        id: 'task-4',
        name: `Another task with ${searchText}`,
        key: `${projectCatalog.key}-4`,
        status: 'Done',
        remoteId: 'jira-task-4',
        remoteUrl: `${projectCatalog.remoteUrl}/browse/${projectCatalog.key}-4`,
        projectCatalogId: projectCatalog.id,
      },
    ]
  }

  /**
   * Finds recent tasks in Jira
   */
  async findMostRecentTasksForProject(
    _z: Zero<Schema>,
    projectCatalog: ProjectCatalogObject,
    _remoteService: RemoteServiceObject
  ): Promise<TaskCatalogObject[]> {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll return some mock data
    return [
      {
        id: 'task-5',
        name: 'Recent Task One',
        key: `${projectCatalog.key}-5`,
        status: 'Open',
        remoteId: 'jira-task-5',
        remoteUrl: `${projectCatalog.remoteUrl}/browse/${projectCatalog.key}-5`,
        projectCatalogId: projectCatalog.id,
        lastUsed: Date.now() - 3600000, // 1 hour ago
      },
      {
        id: 'task-6',
        name: 'Recent Task Two',
        key: `${projectCatalog.key}-6`,
        status: 'In Progress',
        remoteId: 'jira-task-6',
        remoteUrl: `${projectCatalog.remoteUrl}/browse/${projectCatalog.key}-6`,
        projectCatalogId: projectCatalog.id,
        lastUsed: Date.now() - 7200000, // 2 hours ago
      },
    ]
  }

  /**
   * Finds a task in Jira by key
   */
  async findTaskByKey(
    _z: Zero<Schema>,
    issueKey: string,
    projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject | null> {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll return mock data or null
    if (issueKey === `${projectCatalog.key}-404`) {
      return null
    }

    return {
      id: 'task-by-key',
      name: `Task ${issueKey}`,
      key: issueKey,
      status: 'Open',
      remoteId: `jira-task-${issueKey}`,
      remoteUrl: `${projectCatalog.remoteUrl}/browse/${issueKey}`,
      projectCatalogId: projectCatalog.id,
    }
  }

  /**
   * Checks if this adapter supports the given URL
   */
  async supportsUrl(
    url: URL,
    provider: RemoteServiceProvider
  ): Promise<boolean> {
    // Check if the URL is a Jira Cloud URL
    const hostname = url.hostname
    const services = await provider.listRemoteServicesForServiceType(
      this.serviceType
    )
    return (
      hostname.endsWith('.atlassian.net') ||
      services.some((service) => {
        try {
          invariant(service.remoteUrl, 'No remote URL')
          const serviceUrl = new URL(service.remoteUrl)
          return serviceUrl.hostname === hostname
        } catch {
          return false
        }
      })
    )
  }

  /**
   * Parses an issue key from a URL
   */
  async parseIssueKeyFromUrl(
    url: URL,
    provider: RemoteServiceProvider
  ): Promise<ParsingResult | undefined> {
    // Check if this is a Jira URL
    if (!this.supportsUrl(url, provider)) {
      return undefined
    }

    // Try to extract the issue key from the URL
    // Jira URLs typically look like:
    // https://company.atlassian.net/browse/PROJ-123
    const pathname = url.pathname
    if (pathname.includes('/browse/')) {
      const parts = pathname.split('/browse/')
      if (parts.length === 2) {
        const issueKey = parts[1].split('/')[0].trim()
        const projectKey = issueKey.split('-')[0]

        // Find the matching remote service
        const services = await provider.listRemoteServicesForServiceType(
          this.serviceType
        )
        const remoteService = services.find((service) => {
          try {
            invariant(service.remoteUrl, 'No remote URL')
            const serviceUrl = new URL(service.remoteUrl)
            return serviceUrl.hostname === url.hostname
          } catch {
            return false
          }
        })

        if (remoteService && projectKey) {
          return {
            issueKey,
            projectKey,
            remoteServiceObject: remoteService,
          }
        }
      }
    }

    return undefined
  }

  /**
   * Tries to authenticate with Jira
   */
  async tryAuthenticate(
    _z: Zero<Schema>,
    _remoteService: RemoteServiceObject
  ): Promise<boolean> {
    // try {
    // In a real implementation, this would make an API call to Jira
    // For now, we'll simulate a successful response
    return true
    // } catch (error) {
    //   console.error('Error authenticating with Jira:', error)
    //   return {
    //     success: false,
    //     error: error instanceof Error ? error : new Error(String(error)),
    //   }
    // }
  }
}
