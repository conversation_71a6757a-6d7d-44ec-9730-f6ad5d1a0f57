import type { <PERSON>hem<PERSON>, Task, Timerecord } from '@ftt/shared'
import type { DBMuta<PERSON>, Zero } from '@rocicorp/zero'
import { invariant } from 'es-toolkit'
import type { TimeRecordInput } from '~/modules/timerecords/time-record-model'
import type { TimerStatus } from '~/modules/timers/timer-model'

import { clientId } from '~/modules/state/active-client'
import {
  type DbContext,
  dbCreateTimeRecord,
  dbCreateTimer,
  dbDeleteTimeRecord,
  dbDeleteTimer,
  dbResumeTimer,
  dbStopTimer,
  dbUpdateTimeRecord,
  dbUpdateTimer,
} from '../services/database-service'
import type {
  UploadData,
  WorklogObject,
} from '../services/remote-adapters/remote-adapter-types'
import { RemoteServiceManager } from '../services/remote-service-manager'

type TimerecordWithTask = Timerecord & {
  task?: Task
}

/**
 * Helper function to safely convert a database time record to WorklogObject
 */
function toWorklogObject(timeRecord: TimerecordWithTask): WorklogObject {
  return {
    id: timeRecord.id as string,
    taskId: timeRecord.taskId as string,
    startTimestamp: timeRecord.startTimestamp as number,
    endTimestamp: timeRecord.endTimestamp as number | null,
    comment: timeRecord.comment as string | null | undefined,
    uploadData: timeRecord.uploadData as UploadData | null,
    uploadSuccess: timeRecord.uploadSuccess as boolean | null | undefined,
    uploaded: timeRecord.uploaded as number | null | undefined,
    createdAt: timeRecord.createdAt as number | null | undefined,
    updatedAt: timeRecord.updatedAt as number | null | undefined,
    createdBy: timeRecord.createdBy as string | undefined,
    updatedBy: timeRecord.updatedBy as string | undefined,
    task: timeRecord.task as WorklogObject['task'],
  }
}

export interface TimerStruct {
  id: string
  worklogId: string
  status: TimerStatus
}

export interface TimeTrackerState {
  runningTimer?: TimerStruct
  previousTimerRunning: boolean
  isEditDialogOpen: boolean
  editDialogTimeRecordId?: string
  timestampOfIdleTimeStart?: number
  showingTimeoutMessage: boolean
  selectedTaskId?: string
  activeClientId: string
  createdAt: number
  updatedAt: number
}

export function calculateStateHash(state: TimeTrackerState): string {
  const runningTimerId = state.runningTimer?.id ?? 'N/A'
  const isEditDialogOpen = state.isEditDialogOpen ? 'Y' : 'N'
  const showingTimeoutMessage = state.showingTimeoutMessage ? 'Y' : 'N'
  const timestampOfIdleTimeStart = state.timestampOfIdleTimeStart ?? 'N/A'
  //const selectedTaskId = state.selectedTaskId ?? 'N/A'

  return `${runningTimerId}-${isEditDialogOpen}-${showingTimeoutMessage}-${timestampOfIdleTimeStart}`
}

// =============
// timer management
// =============

export function canStopTimer(state: TimeTrackerState) {
  return (
    !state.showingTimeoutMessage &&
    !state.isEditDialogOpen &&
    state.runningTimer
  )
}

export interface AppState {
  runningTimerId?: string | null
  isEditDialogOpen: boolean | null
  editDialogTimeRecordId?: string | null
  timestampOfIdleTimeStart?: number | null
  showingTimeoutMessage: boolean | null
  activeClientId: string | null
  createdAt: number | null
  updatedAt: number | null
}

export function canStopTimerFromDb(appState: AppState) {
  return (
    !appState.showingTimeoutMessage &&
    !appState.isEditDialogOpen &&
    !!appState.runningTimerId
  )
}

export async function startTimer(z: Zero<Schema>, taskId: string) {
  await z.mutateBatch(async (tx) => {
    await doStartTimer(z, tx, taskId)
  })
}

async function doStartTimer(
  z: Zero<Schema>,
  tx: DBMutator<Schema>,
  taskId: string
) {
  const timer = await dbCreateTimer(z, tx, taskId)
  tx.appState.update({
    id: z.userID,
    runningTimerId: timer.id,
    timestampOfIdleTimeStart: null,
    showingTimeoutMessage: false,
    activeClientId: clientId,
    updatedAt: Date.now(),
  })
}

export async function stopTimer(
  z: Zero<Schema>,
  timerId: string,
  stopTimestamp: number
) {
  invariant(timerId, 'No running timer id')
  await z.mutateBatch(async (tx) => {
    const timeRecordId = await dbStopTimer(z, tx, timerId, stopTimestamp)

    await tx.appState.update({
      id: z.userID,
      isEditDialogOpen: true,
      editDialogTimeRecordId: timeRecordId,
      timestampOfIdleTimeStart: null,
      showingTimeoutMessage: false,
      activeClientId: clientId,
      updatedAt: Date.now(),
    })
  })
}

export async function resumeTimer(z: Zero<Schema>, timerId: string) {
  invariant(timerId, 'No timer id provided')

  const appState = await z.query.appState
    .where('id', '=', z.userID)
    .related('runningTimer')
    .one()
    .run()
  invariant(appState, 'No app state')
  const timer = appState.runningTimer
  invariant(timer, 'No running timer')
  console.log('resuming timer', { timerId, worklogId: timer.worklogId })
  try {
    await z.mutateBatch(async (tx) => {
      await dbResumeTimer(z, tx, timerId, timer.worklogId)
      await tx.appState.update({
        id: z.userID,
        runningTimerId: timerId,
        timestampOfIdleTimeStart: null,
        showingTimeoutMessage: false,
        activeClientId: clientId,
        updatedAt: Date.now(),
      })
    })
  } catch (error) {
    console.error('Error resuming timer:', error)
    throw error
  }
}

/**
 * Stops the current timer at the idle timestamp and starts a new timer for the same task
 */
export async function stopAndRestartTimer(
  z: Zero<Schema>,
  timerId: string,
  stopTimestamp: number
) {
  invariant(timerId, 'No running timer id')

  // First, get the current timer to extract the task ID
  const timer = await z.query.timers
    .where('id', '=', timerId)
    .related('timerecord', (q) => q.related('task'))
    .one()
    .run()

  invariant(timer, 'Timer not found')
  invariant(timer.timerecord, 'Timerecord not found')
  invariant(timer.timerecord.task, 'Task not found')

  const timeRecordId = timer.timerecord.id
  const taskId = timer.timerecord.task.id

  // Stop the current timer
  await z.mutateBatch(async (tx) => {
    await dbStopTimer(z, tx, timerId, stopTimestamp)

    // create timer and time record for the same task
    const timer = await dbCreateTimer(z, tx, taskId)
    tx.appState.update({
      id: z.userID,
      runningTimerId: timer.id,
      isEditDialogOpen: true,
      editDialogTimeRecordId: timeRecordId,
      timestampOfIdleTimeStart: null,
      showingTimeoutMessage: false,
      activeClientId: clientId,
      updatedAt: Date.now(),
    })
  })
}

export async function idleTimerExpired(z: Zero<Schema>, stopTimestamp: number) {
  console.log('idle timer expired', stopTimestamp)
  const appState = await z.query.appState
    .where('id', '=', z.userID)
    .related('runningTimer')
    .one()
    .run()
  invariant(appState, 'No app state')
  const running = appState.runningTimer
  invariant(running, 'No running timer')
  const timerId = running.id
  invariant(running.status === 'running', 'Timer is not running')

  // Update app state with idle time information
  await z.mutateBatch(async (tx) => {
    const now = Date.now()
    await dbStopTimer(z, tx, timerId, stopTimestamp)

    // Update remote state
    await tx.appState.update({
      id: z.userID,
      timestampOfIdleTimeStart: stopTimestamp,
      showingTimeoutMessage: true,
      activeClientId: clientId,
      updatedAt: now,
    })
  })
}

async function getAppState(z: Zero<Schema>) {
  const appState = await z.query.appState
    .where('id', '=', z.userID)
    .related('runningTimer')
    .one()
    .run()
  invariant(appState, 'No app state')
  return appState
}

export async function timeRecordEditorClosed(
  z: Zero<Schema>,
  timeRecord: TimeRecordInput
) {
  console.log('time record editor closed')
  const appState = await getAppState(z)
  const timer = appState.runningTimer

  await z.mutateBatch(async (tx) => {
    await dbUpdateTimeRecord(z, tx, timeRecord)

    // check if the running timers timerecord matches with the record we were editing
    if (timer && timer.worklogId === timeRecord.id) {
      // first clear the ref
      console.log('updating app state, deleting timer ref')
      await tx.appState.update({
        id: z.userID,
        isEditDialogOpen: false,
        editDialogTimeRecordId: null,
        runningTimerId: null,
        timestampOfIdleTimeStart: null,
        showingTimeoutMessage: false,
        activeClientId: clientId,
        updatedAt: Date.now(),
      })
      console.log('deleting timer', timer.id)
      await dbDeleteTimer(z, tx, timer.id)
    } else {
      await tx.appState.update({
        id: z.userID,
        isEditDialogOpen: false,
        editDialogTimeRecordId: null,
        activeClientId: clientId,
        updatedAt: Date.now(),
      })
    }
  })

  // After the database transaction is complete, sync with remote service
  try {
    // Get the full time record with task information
    const fullTimeRecord = await z.query.timerecords
      .where('id', '=', timeRecord.id)
      .related('task', (q) => q.related('project'))
      .one()
      .run()

    if (fullTimeRecord) {
      // Create a remote service manager and sync the worklog
      const remoteServiceManager = new RemoteServiceManager(z)
      await remoteServiceManager.syncWorklog(toWorklogObject(fullTimeRecord))
    }
  } catch (error) {
    console.error(
      'Error syncing time record with remote service after editor closed:',
      error
    )
    // Don't throw the error - we don't want to fail the update if remote sync fails
  }
}

export async function timeRecordEditorCancelled(z: Zero<Schema>) {
  const appState = await z.query.appState
    .where('id', '=', z.userID)
    .related('runningTimer')
    .one()
    .run()
  invariant(appState, 'No app state')
  const timer = appState.runningTimer
  invariant(timer, 'No running timer')

  await z.mutateBatch(async (tx) => {
    await tx.appState.update({
      id: z.userID,
      isEditDialogOpen: false,
      editDialogTimeRecordId: null,
      runningTimerId: null,
      timestampOfIdleTimeStart: null,
      showingTimeoutMessage: false,
      activeClientId: clientId,
      updatedAt: Date.now(),
    })
  })
}

// ==============
// Time record management
// ==============

export async function updateTimeRecord(
  z: Zero<Schema>,
  timeRecord: TimeRecordInput
) {
  const appState = await z.query.appState
    .where('id', '=', z.userID)
    .related('runningTimer')
    .one()
    .run()

  await z.mutateBatch(async (tx) => {
    await dbUpdateTimeRecord(z, tx, timeRecord)
    const timeRecordId = timeRecord.id
    // now check if there is a running timer
    invariant(appState, 'No app state')

    const running = appState.runningTimer
    if (running) {
      const startStopHasChanged =
        timeRecord.startTimestamp !== running.startTimestamp ||
        timeRecord.endTimestamp !== running.endTimestamp
      if (startStopHasChanged) {
        // update the start time to match with the time record
        await dbUpdateTimer(z, tx, running.id, {
          timeRecordId,
          startTimestamp: timeRecord.startTimestamp ?? running.startTimestamp,
          endTimestamp:
            timeRecord.endTimestamp ?? running.endTimestamp ?? undefined,
          status: running.status,
        })
      }
    }
    await tx.appState.update({
      id: z.userID,
      isEditDialogOpen: false,
      editDialogTimeRecordId: null,
      activeClientId: clientId,
      updatedAt: Date.now(),
    })
  })

  // After the database transaction is complete, sync with remote service
  try {
    // Get the full time record with task information
    const fullTimeRecord = await z.query.timerecords
      .where('id', '=', timeRecord.id)
      .related('task', (q) => q.related('project'))
      .one()
      .run()

    if (fullTimeRecord) {
      // Create a remote service manager and sync the worklog
      const remoteServiceManager = new RemoteServiceManager(z)
      await remoteServiceManager.syncWorklog(toWorklogObject(fullTimeRecord))
    }
  } catch (error) {
    console.error('Error syncing time record with remote service:', error)
    // Don't throw the error - we don't want to fail the update if remote sync fails
  }
}

export async function createTimeRecord(
  z: Zero<Schema>,
  timeRecord: TimeRecordInput
) {
  await dbCreateTimeRecord(z, timeRecord)

  // After creating the time record, sync with remote service
  try {
    // Get the full time record with task information
    const fullTimeRecord = await z.query.timerecords
      .where('id', '=', timeRecord.id)
      .related('task', (q) => q.related('project'))
      .one()
      .run()

    if (fullTimeRecord) {
      // Create a remote service manager and sync the worklog
      const remoteServiceManager = new RemoteServiceManager(z)
      await remoteServiceManager.syncWorklog(toWorklogObject(fullTimeRecord))
    }
  } catch (error) {
    console.error('Error syncing new time record with remote service:', error)
    // Don't throw the error - we don't want to fail the creation if remote sync fails
  }
}

export async function makeCurrentClientActive(z: Zero<Schema>) {
  console.log('making current client active', clientId)
  await z.mutate.appState.update({
    id: z.userID,
    activeClientId: clientId,
    updatedAt: Date.now(),
  })
}

export async function deleteTimeRecord(
  context: DbContext,
  timeRecordId: string
) {
  // First get the full time record with upload data
  const { z } = context
  const timeRecord = await z.query.timerecords
    .where('id', '=', timeRecordId)
    .one()
    .run()

  if (!timeRecord) {
    console.warn(`Time record with ID ${timeRecordId} not found`)
    return
  }

  // If the time record has upload data, delete it from the remote service
  if (timeRecord.uploadData && typeof timeRecord.uploadData === 'object') {
    const uploadData = timeRecord.uploadData as Record<string, unknown>
    const remoteServiceId = uploadData.remoteServiceId as string
    if (remoteServiceId && typeof remoteServiceId === 'string') {
      // Get the remote service
      const remoteService = await z.query.remoteServices
        .where('id', '=', remoteServiceId)
        .one()
        .run()

      if (remoteService) {
        // Delete from remote service
        const remoteServiceManager = new RemoteServiceManager(z)
        await remoteServiceManager.deleteWorklog(
          toWorklogObject(timeRecord),
          remoteService
        )
      }
    }
  }

  await dbDeleteTimeRecord(context.z, timeRecordId)
}
