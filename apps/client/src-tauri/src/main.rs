// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri_plugin_window_state;
use user_idle::UserIdle;

// Learn more about Tauri commands at https://v1.tauri.app/v1/guides/features/command
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// Get the system idle time in milliseconds
#[tauri::command]
fn get_idle_time() -> Result<u64, String> {
    match UserIdle::get_time() {
        Ok(idle) => Ok(idle.as_milliseconds() as u64),
        Err(err) => Err(format!("Failed to get idle time: {}", err)),
    }
}

fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_window_state::Builder::new().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .invoke_handler(tauri::generate_handler![
            greet,
            get_idle_time,
            is_window_focused
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
async fn is_window_focused(window: tauri::Window) -> Result<bool, String> {
    Ok(window.is_focused().map_err(|e| e.to_string())?)
}
