{"identifier": "migrated", "description": "permissions that were migrated from v1", "local": true, "windows": ["main"], "permissions": ["core:default", "shell:allow-open", "shell:default", "dialog:default", "window-state:default", "fs:allow-home-write-recursive", "fs:default", {"identifier": "fs:allow-exists", "allow": [{"path": "$APPDATA/*"}, {"path": "$LOCALAPPDATA/*"}, {"path": "$HOME/*"}]}, "http:allow-fetch", "http:allow-fetch-cancel", "http:allow-fetch-read-body", "http:allow-fetch-send", {"identifier": "http:default", "allow": [{"url": "https://*.gofore.com"}, {"url": "http://localhost:9999"}], "deny": []}, "http:default"]}